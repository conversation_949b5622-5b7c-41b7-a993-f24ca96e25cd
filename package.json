{"name": "ehs", "version": "1.0.0", "type": "module", "engines": {"node": ">=20"}, "prettier": {"semi": true, "singleQuote": true, "trailingComma": "all", "arrowParens": "always", "printWidth": 120, "tabWidth": 2, "useTabs": false}, "scripts": {"dev": "nodemon -w server --inspect=18594 -x tsx server/index.ts", "build": "vite build", "start": "tsx server/index.ts", "check": "tsc", "serveo": "ssh -R ehsupkapp.serveo.net:80:localhost:8594 serveo.net", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:drop": "drizzle-kit drop", "db:check": "drizzle-kit check", "db:seed": "tsx server/db/seed/index.ts", "test": "pnpm test:db:setup && vitest run --pool=threads --poolOptions.threads.singleThread", "test:watch": "pnpm test:db:setup && vitest --pool=threads --poolOptions.threads.singleThread", "test:coverage": "pnpm test:db:setup && vitest run --coverage --pool=threads --poolOptions.threads.singleThread", "test:server": "pnpm test:db:setup && find server -name \"*.test.ts\" | xargs npx vitest run --pool=threads --poolOptions.threads.singleThread", "test:server:coverage": "pnpm test:db:setup && find server -name \"*.test.ts\" | xargs npx vitest run --coverage --pool=forks", "test:db:setup": "drizzle-kit migrate --config drizzle-test.config.ts", "lint": "eslint \"**/*.{ts,tsx}\" --max-warnings=0", "lint:fix": "eslint \"**/*.{ts,tsx}\" --fix", "lint:server": "eslint \"server/**/*.ts\" --max-warnings=0", "lint:server:fix": "eslint \"server/**/*.ts\" --fix", "job:capas:overdue": "tsx server/jobs/overdue-notification.ts", "app:logs": "docker-compose logs -f ehs", "ehs-drop-database": "node jobs/ehs-drop-database.js"}, "dependencies": {"@ai-sdk/openai": "^2.0.32", "@aws-sdk/client-s3": "^3.891.0", "@aws-sdk/s3-request-presigner": "^3.891.0", "@date-fns/tz": "^1.4.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.2.2", "@instana/collector": "^4.24.0", "@mailchimp/mailchimp_transactional": "^1.0.59", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/colors": "^3.0.0", "@stepperize/react": "^5.1.8", "@t3-oss/env-core": "^0.13.8", "@tailwindcss/vite": "^4.1.13", "@tanstack/react-query": "^5.89.0", "@tanstack/react-query-devtools": "^5.89.0", "@trpc-limiter/memory": "^1.0.0", "@trpc/client": "^11.5.1", "@trpc/react-query": "^11.5.1", "@trpc/server": "^11.5.1", "@trpc/tanstack-react-query": "^11.5.1", "@uidotdev/usehooks": "^2.4.1", "ai": "^5.0.45", "axios": "^1.12.2", "axios-retry": "^4.5.0", "bullmq": "^5.58.5", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^17.2.2", "drizzle-orm": "^0.44.5", "drizzle-zod": "^0.8.3", "dromo-uploader-react": "^2.1.11", "embla-carousel-react": "^8.6.0", "express": "^5.1.0", "fastest-levenshtein": "^1.0.16", "heic-convert": "^2.1.0", "ioredis": "^5.7.0", "lucide-react": "^0.542.0", "mixpanel-browser": "^2.70.0", "motion": "^12.23.14", "pg": "^8.16.3", "qrcode.react": "^4.2.0", "radix-ui": "^1.4.3", "react": "^19.1.1", "react-day-picker": "^9.10.0", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "sharp": "^0.34.4", "sonner": "^2.0.7", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vite-express": "^0.21.1", "winston": "^3.17.0", "wouter": "^3.7.1", "zod": "^4.1.9"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.13", "@tailwindcss/typography": "^0.5.16", "@testing-library/react": "^16.3.0", "@types/canvas-confetti": "^1.9.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/heic-convert": "^2.1.0", "@types/mailchimp__mailchimp_transactional": "^1.0.11", "@types/mixpanel": "^2.14.9", "@types/node": "^24.5.1", "@types/pg": "^8.15.5", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.44.0", "@typescript-eslint/parser": "^8.44.0", "@vitejs/plugin-react": "^5.0.3", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "drizzle-kit": "^0.31.4", "eslint": "^9.35.0", "eslint-plugin-vitest": "^0.5.4", "jsdom": "^26.1.0", "nodemon": "^3.1.10", "prettier": "^3.6.2", "supertest": "^7.1.4", "tailwindcss": "^4.1.13", "tsx": "^4.20.5", "tw-animate-css": "^1.3.8", "typescript": "^5.9.2", "vite": "^7.1.5", "vite-plugin-runtime": "^1.4.0", "vitest": "^3.2.4"}, "pnpm": {"overrides": {"esbuild": "^0.25.5"}, "ignoredBuiltDependencies": ["esbuild"], "onlyBuiltDependencies": ["@instana/autoprofile", "@tailwindcss/oxide", "esbuild", "event-loop-stats", "gcstats.js"]}, "packageManager": "pnpm@10.15.1+sha512.34e538c329b5553014ca8e8f4535997f96180a1d0f614339357449935350d924e22f8614682191264ec33d1462ac21561aff97f6bb18065351c162c7e8f6de67"}