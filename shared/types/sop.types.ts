import { createInsertSchema, createUpdateSchema } from 'drizzle-zod';
import { z } from 'zod';
import {
  sops,
  sopSection,
  sopSectionTypeEnum,
  approvalStatusEnum,
  hazardCategoryEnum,
  controlMeasureCategoryEnum,
} from '../schema';
import { PaginationInputSchema, SortInputSchema, UuidSchema } from './schema.types';

// Suggested labels for different section types
export const SOP_SECTION_LABELS: Record<(typeof sopSectionTypeEnum.enumValues)[number], string[]> = {
  general: [
    'Scope & Applicability',
    'Target Audience',
    'Equipment & Materials',
    'Prerequisites & Additional Notes',
    'Definitions',
    'Regulatory Basis & References',
    'Training & Competency Requirements',
    'Custom',
  ],
  emergency: [
    'Potential Emergencies',
    'Emergency Contacts',
    'Evacuation Procedures',
    'First Aid Response',
    'Incident Reporting Process',
  ],
  step: [], // Dynamic labels like "Hazard 1", "Hazard 2" etc.
  pre_procedure: [], // User can add any labels
  procedure: [], // User can add any labels
  post_procedure: [], // User can add any labels
};

// Enums for filtering
export const SopReviewStatusEnum = z.enum(['overdue', 'due_soon', 'no_review_date']);
export const SopRiskLevelEnum = z.enum(['high', 'medium', 'low']);

// Maps for display
export const SOP_REVIEW_STATUS_MAP: Record<z.infer<typeof SopReviewStatusEnum>, string> = {
  [SopReviewStatusEnum.enum.overdue]: 'Overdue',
  [SopReviewStatusEnum.enum.due_soon]: 'Due Soon',
  [SopReviewStatusEnum.enum.no_review_date]: 'No Review Date',
};

export const SOP_RISK_LEVEL_MAP: Record<z.infer<typeof SopRiskLevelEnum>, string> = {
  [SopRiskLevelEnum.enum.high]: 'High',
  [SopRiskLevelEnum.enum.medium]: 'Medium',
  [SopRiskLevelEnum.enum.low]: 'Low',
};

export const SOP_STATUS_MAP: Record<(typeof approvalStatusEnum.enumValues)[number], string> = {
  [approvalStatusEnum.enumValues[0]]: 'Draft',
  [approvalStatusEnum.enumValues[1]]: 'Under Review',
  [approvalStatusEnum.enumValues[2]]: 'Approved/Active',
};

// Get risk level based on severity and likelihood scores
export const getSopRiskLevel = (severity: number, likelihood: number) => {
  const score = severity * likelihood;
  if (score >= 15)
    return {
      level: 'High',
      color: 'bg-red-100 text-red-800 border-red-200',
      iconType: 'alert',
    };
  if (score >= 6)
    return {
      level: 'Medium',
      color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      iconType: 'flag',
    };
  return {
    level: 'Low',
    color: 'bg-green-100 text-green-800 border-green-200',
  };
};

// Filter schemas
export const SopFiltersSchema = z.object({
  search: z.string().optional(),
  mustIncludeObjectIds: z.array(z.string()).optional(),
  status: z.array(z.enum(approvalStatusEnum.enumValues)).optional(),
  ownerId: z.array(z.string()).optional(),
  riskLevel: SopRiskLevelEnum.optional(), // Filter by risk level: 'high' (>=15), 'medium' (>=6), 'low' (<6) based on severity * likelihood
  reviewStatus: SopReviewStatusEnum.optional(),
  includeArchived: z.boolean().optional(),
  locationIds: z.array(z.string()).optional(),
  includeLocation: z.boolean().optional(),
});

export type SopFilters = z.infer<typeof SopFiltersSchema>;

export const ListSopSchema = PaginationInputSchema.and(SortInputSchema).and(SopFiltersSchema);

export const ExportSopSchema = SortInputSchema.and(SopFiltersSchema);

const SopValidations = {
  title: z.string().min(1, 'Sop Title is required'),
  ownerId: z.string({ error: 'Owner is required' }),
  approverId: z.string({ error: 'Approver is required' }),
};

// SOP schemas
const SopInsertSchema = createInsertSchema(sops, SopValidations).omit({
  id: true,
  upkeepCompanyId: true,
  slug: true,
  version: true,
  highestSeverity: true,
  createdBy: true,
  createdAt: true,
  updatedAt: true,
  archivedAt: true,
});

const SopUpdateSchema = createUpdateSchema(sops, {
  id: z.cuid2(),
  ...SopValidations,
}).omit({
  upkeepCompanyId: true,
  slug: true,
  version: true,
  highestSeverity: true,
  createdBy: true,
  createdAt: true,
});

// Items to create schema for hazards and control measures
const ItemsToCreateSchema = z.object({
  hazardsToCreate: z
    .array(
      z.object({
        name: z.string().min(1, 'Hazard name is required'),
        type: z.enum(hazardCategoryEnum.enumValues),
      }),
    )
    .nullish(),
  controlMeasuresToCreate: z
    .array(
      z.object({
        name: z.string().min(1, 'Control measure name is required'),
        type: z.enum(controlMeasureCategoryEnum.enumValues),
      }),
    )
    .nullish(),
});

// SOP Section validations
const SopSectionValidations = {
  value: z.string().min(1, 'Section value is required'),
  sectionType: z.enum(sopSectionTypeEnum.enumValues),
  // Note: serial must be unique per sectionType (e.g., general sections: 1,2,3; step sections: 1,2,3)
};

// Create SOP Section schema with conditional validation
export const CreateSopSectionSchema = createInsertSchema(sopSection, {
  ...SopSectionValidations,
  serial: z.number().int().positive(), // Required for creation, must be unique per sectionType
})
  .omit({
    id: true,
    upkeepCompanyId: true,
    sopId: true,
    createdBy: true,
    createdAt: true,
    archivedAt: true,
    updatedAt: true,
  })
  .and(ItemsToCreateSchema)
  .refine(
    (data) => {
      // If section type is 'step', severity and likelihood are required
      if (data.sectionType === 'step') {
        if (!data.severity || data.severity < 1 || data.severity > 4) {
          return false;
        }
        if (!data.likelihood || data.likelihood < 1 || data.likelihood > 5) {
          return false;
        }
      }
      return true;
    },
    {
      message: 'Severity (1-4) and likelihood (1-5) are required for step sections',
      path: ['severity'],
    },
  );

// Update SOP Section schema
export const UpdateSopSectionSchema = createUpdateSchema(sopSection, {
  ...SopSectionValidations,
  serial: z.number().int().positive().optional(), // Optional for reordering, must be unique per sectionType
})
  .omit({
    upkeepCompanyId: true,
    sopId: true,
    createdBy: true,
    createdAt: true,
    archivedAt: true,
    updatedAt: true,
  })
  .and(ItemsToCreateSchema)
  .refine(
    (data) => {
      // If section type is 'step', severity and likelihood are required
      if (data.sectionType === 'step') {
        if (!data.severity || data.severity < 1 || data.severity > 4) {
          return false;
        }
        if (!data.likelihood || data.likelihood < 1 || data.likelihood > 5) {
          return false;
        }
      }
      return true;
    },
    {
      message: 'Severity (1-4) and likelihood (1-5) are required for step sections',
      path: ['severity'],
    },
  );

// Get SOP by instance ID schema
export const GetSopByInstanceIdSchema = UuidSchema.extend({
  versionId: z.cuid2().nullish(),
});

// Create SOP schema
export const CreateSopFormSchema = z.object({
  sop: SopInsertSchema,
  sections: z.array(CreateSopSectionSchema).min(1, 'At least one section is required'),
});

export const CreateSopAiSchema = CreateSopFormSchema.extend({
  sop: SopInsertSchema.omit({ reviewDate: true }),
});

// Update SOP schema
export const UpdateSopFormSchema = z.object({
  sop: SopUpdateSchema,
  sections: z.array(UpdateSopSectionSchema).min(1, 'At least one section is required'),
});

// Update SOP status schema
export const UpdateSopStatusSchema = z
  .object({
    id: z.cuid2(),
    status: z.enum(approvalStatusEnum.enumValues),
    rejectionReason: z.string().nullish(),
  })
  .refine(
    (data) => {
      // If status is 'draft' (rejection), rejectionReason is required
      if (data.status === approvalStatusEnum.enumValues[0] && !data.rejectionReason?.trim()) {
        return false;
      }
      return true;
    },
    {
      message: 'Rejection reason is required when rejecting an SOP',
      path: ['rejectionReason'],
    },
  );

// Type definitions
export type UpdateSopStatus = z.infer<typeof UpdateSopStatusSchema>;
export type ToggleArchiveSopType = z.infer<typeof UuidSchema>;
export type CreateSopType = z.infer<typeof CreateSopFormSchema>;
export type UpdateSopType = z.infer<typeof UpdateSopFormSchema>;
export type CreateSopSectionType = z.infer<typeof CreateSopSectionSchema>;
export type UpdateSopSectionType = z.infer<typeof UpdateSopSectionSchema>;

// Helper to generate step labels
export const generateStepLabel = (index: number): string => `Hazard ${index + 1}`;

// Section type display map
export const SOP_SECTION_TYPE_MAP: Record<(typeof sopSectionTypeEnum.enumValues)[number], string> = {
  general: 'General Information',
  emergency: 'Emergency Procedures',
  step: 'Step-by-Step Procedure',
  pre_procedure: 'Pre-Procedure',
  procedure: 'Procedure',
  post_procedure: 'Post-Procedure',
};

// Severity levels for steps
export const SOP_SEVERITY_LEVELS = [
  { value: 1, label: 'Low', color: 'bg-green-100 text-green-800' },
  { value: 2, label: 'Moderate', color: 'bg-yellow-100 text-yellow-800' },
  { value: 3, label: 'High', color: 'bg-orange-100 text-orange-800' },
  { value: 4, label: 'Critical', color: 'bg-red-100 text-red-800' },
] as const;
