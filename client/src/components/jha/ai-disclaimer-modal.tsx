import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

type AiDisclaimerModalProps = {
  open: boolean;
  onAccept: () => void;
  onCancel: () => void;
};

export const AiDisclaimerModal = ({ open, onAccept, onCancel }: AiDisclaimerModalProps) => {
  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onCancel()}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-gray-900">
            AI Content Disclaimer – User Responsibility
          </DialogTitle>
          <DialogDescription className="sr-only">Disclaimer for AI-generated content responsibility</DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="prose prose-sm max-w-none">
            <p className="text-gray-700 leading-relaxed">
              The content generated by this AI tool is provided for{' '}
              <strong>drafting and informational purposes only</strong>. It is not a substitute for professional safety,
              legal, or compliance advice.
            </p>

            <p className="text-gray-700 leading-relaxed">
              <strong>You, as the author</strong>, retain full responsibility and liability for reviewing,
              fact-checking, and ensuring the accuracy, completeness, and compliance of all information before
              submission or use.
            </p>

            <p className="text-gray-700 leading-relaxed">
              By proceeding, you acknowledge that{' '}
              <strong>UpKeep is not liable for any outcomes, claims, or damages</strong> resulting from reliance on
              AI-generated content.
            </p>
          </div>
        </div>

        <DialogFooter className="flex flex-col-reverse gap-2 sm:flex-row sm:justify-end">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button onClick={onAccept} className="bg-blue-600 hover:bg-blue-700 text-white">
            I Accept
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
