import { FormControl, FormDescription, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { WritingAssistant } from '@/components/ui/writing-assistant';
import { cn } from '@/lib/utils';
import type React from 'react';
import { ControllerRenderProps, FieldPath, FieldValues } from 'react-hook-form';

type TextareaWithWritingAssistantProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = {
  /** The field object from react-hook-form */
  field: ControllerRenderProps<TFieldValues, TName>;
  /** Label for the textarea */
  label: string;
  /** Placeholder text for the textarea */
  placeholder?: string;
  /** Description text below the textarea */
  description?: string;
  /** Context for the AI writing assistant */
  context: string;
  /** Whether the field is required */
  required?: boolean;
  /** Additional className for the textarea */
  className?: string;
  /** Whether the component is disabled */
  disabled?: boolean;
  /** Minimum height for the textarea */
  minHeight?: string;
};

export function TextareaWithWritingAssistant<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  field,
  label,
  placeholder,
  description,
  context,
  required = false,
  className,
  disabled = false,
  minHeight = '80px',
}: TextareaWithWritingAssistantProps<TFieldValues, TName>) {
  const handleReplace = (improvedText: string) => {
    field.onChange(improvedText);
  };

  return (
    <FormItem>
      <FormLabel>
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </FormLabel>
      <FormControl>
        <div className="relative">
          <Textarea
            {...field}
            placeholder={placeholder}
            className={cn(`min-h-[${minHeight}] resize-none pr-12`, className)}
            value={field.value ?? ''}
            disabled={disabled}
          />
          <div className="absolute bottom-2 right-2">
            <WritingAssistant
              text={field.value ?? ''}
              context={context}
              onReplace={handleReplace}
              disabled={disabled}
              className="text-xs h-8 px-2"
            />
          </div>
        </div>
      </FormControl>
      {description && <FormDescription>{description}</FormDescription>}
      <FormMessage />
    </FormItem>
  );
}
