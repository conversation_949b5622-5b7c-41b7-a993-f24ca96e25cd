import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { trpc } from '@/providers/trpc';
import { <PERSON><PERSON>, Loader2, RotateCcw, Sparkles, X } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

/**
 * WritingAssistant Component
 *
 * A reusable component that provides AI-powered text improvement functionality.
 * Similar to <PERSON><PERSON><PERSON>'s writing assistant, it allows users to enhance their text
 * for clarity, tone, and professionalism.
 *
 * @example
 * // Basic usage
 * <WritingAssistant
 *   text={currentText}
 *   context="Safety incident description"
 *   onReplace={(improvedText) => setCurrentText(improvedText)}
 * />
 *
 * @example
 * // With FormField (use TextareaWithWritingAssistant instead)
 * <FormField
 *   control={form.control}
 *   name="description"
 *   render={({ field }) => (
 *     <TextareaWithWritingAssistant
 *       field={field}
 *       label="Description"
 *       context="Safety incident description"
 *       placeholder="Enter description..."
 *     />
 *   )}
 * />
 */

type WritingAssistantProps = {
  /** The current text content to improve */
  text: string;
  /** Context for the AI to understand what kind of improvement is needed */
  context: string;
  /** Callback when user chooses to replace the text */
  onReplace: (improvedText: string) => void;
  /** Optional className for the trigger button */
  className?: string;
  /** Whether the component is disabled */
  disabled?: boolean;
};

export function WritingAssistant({ text, context, onReplace, className, disabled = false }: WritingAssistantProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [improvedText, setImprovedText] = useState<string>('');

  const { mutateAsync: improveText, isPending: isImproving } = trpc.ai.improveText.useMutation({
    onSuccess: (data) => {
      setImprovedText(data);
      toast.success('Text improved', {
        description: 'Your text has been enhanced for clarity and professionalism.',
      });
    },
    onError: (error) => {
      console.error('Error improving text:', error);
      toast.error('Error improving text', {
        description: 'There was a problem improving your text. Please try again.',
      });
    },
  });

  const handleOpenAssistant = async () => {
    if (!text.trim()) {
      toast.error('No text to improve', {
        description: 'Please enter some text before using the writing assistant.',
      });
      return;
    }

    setIsOpen(true);
    setImprovedText('');

    // Automatically start improving the text when modal opens
    try {
      await improveText({ text: text.trim(), context });
    } catch {
      // Error is already handled in the mutation
    }
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(improvedText);
      toast.success('Copied to clipboard', {
        description: 'The improved text has been copied to your clipboard.',
      });
    } catch {
      toast.error('Failed to copy', {
        description: 'Could not copy text to clipboard.',
      });
    }
  };

  const handleReplace = () => {
    onReplace(improvedText);
    setIsOpen(false);
    toast.success('Text replaced', {
      description: 'Your text has been updated with the improved version.',
    });
  };

  const handleRegenerate = async () => {
    if (!text.trim()) return;

    setImprovedText('');
    try {
      await improveText({ text: text.trim(), context });
    } catch {
      // Error is already handled in the mutation
    }
  };

  return (
    <>
      {/* Trigger Button */}
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={handleOpenAssistant}
        disabled={disabled || !text.trim()}
        className={className}
      >
        <Sparkles className="h-4 w-4 mr-2" />
        Writing Assist
      </Button>

      {/* Modal Dialog */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-purple-600" />
              Here's a version with suggested improvements
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-auto space-y-4">
            {/* Loading State */}
            {isImproving && (
              <div className="flex items-center justify-center py-8">
                <div className="flex items-center gap-3">
                  <Loader2 className="h-5 w-5 animate-spin text-purple-600" />
                  <span className="text-sm text-muted-foreground">Improving your text...</span>
                </div>
              </div>
            )}

            {/* Improved Text Display */}
            {improvedText && !isImproving && (
              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4 border">
                  <Textarea
                    value={improvedText}
                    readOnly
                    className="min-h-[200px] resize-none border-none bg-transparent p-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                  />
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Button type="button" variant="outline" size="sm" onClick={handleRegenerate} disabled={isImproving}>
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Regenerate
                    </Button>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button type="button" variant="outline" onClick={handleCopy} disabled={isImproving}>
                      <Copy className="h-4 w-4 mr-2" />
                      Copy
                    </Button>
                    <Button type="button" onClick={handleReplace} disabled={isImproving}>
                      Replace
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Error State */}
            {!isImproving && !improvedText && isOpen && (
              <div className="flex items-center justify-center py-8">
                <div className="text-center">
                  <X className="h-8 w-8 text-red-500 mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">Failed to improve text. Please try again.</p>
                  <Button type="button" variant="outline" size="sm" onClick={handleRegenerate} className="mt-2">
                    Try Again
                  </Button>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
