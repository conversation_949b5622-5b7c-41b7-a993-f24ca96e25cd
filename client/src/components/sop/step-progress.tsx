import { useStepper, utils } from '@/components/sop/stepper';
import { cn } from '@/lib/utils';
import { Check, AlertCircle } from 'lucide-react';
import { useFormContext } from 'react-hook-form';

export const StepProgress = () => {
  const stepper = useStepper();

  const form = useFormContext();

  const requiredFields = {
    'general-information': ['sop.title'],
    'hazards-and-control': ['sections.0.value'],
    metadata: ['sop.ownerId', 'sop.approverId'],
  };

  // Helper function to get nested error by dot notation path
  const getNestedError = (errors: Record<string, unknown>, path: string): unknown => {
    if (!errors || !path) return undefined;

    return path.split('.').reduce((current: unknown, key) => {
      if (current === null || current === undefined) return undefined;

      // Handle array indices (e.g., "0", "1", etc.)
      if (!isNaN(Number(key))) {
        return Array.isArray(current) ? current[Number(key)] : undefined;
      }

      return current && typeof current === 'object' && key in current
        ? (current as Record<string, unknown>)[key]
        : undefined;
    }, errors);
  };

  // Check if a step has validation errors
  const getStepErrors = (stepId: string): boolean => {
    const fields = requiredFields[stepId as keyof typeof requiredFields];
    if (!fields || !form?.formState?.errors) return false;

    return fields.some((fieldPath) => {
      const error = getNestedError(form.formState.errors, fieldPath);
      return error !== undefined && error !== null;
    });
  };

  const currentStepIndex = utils.getIndex(stepper.current.id);
  const totalSteps = stepper.all.length;

  return (
    <div className="w-full py-6">
      {/* Mobile Progress Bar */}
      <div className="block md:hidden mb-4">
        <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
          <span>
            Step {currentStepIndex + 1} of {totalSteps}
          </span>
          <span>{Math.round(((currentStepIndex + 1) / totalSteps) * 100)}% Complete</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={cn(
              'h-2 rounded-full transition-all duration-300',
              getStepErrors(stepper.current.id) ? 'bg-red-500' : 'bg-blue-500',
            )}
            style={{ width: `${((currentStepIndex + 1) / totalSteps) * 100}%` }}
          />
        </div>
        <div className="mt-2 text-center flex items-center justify-center gap-1">
          {getStepErrors(stepper.current.id) && <AlertCircle className="w-4 h-4 text-red-500" />}
          <p className={cn('font-medium', getStepErrors(stepper.current.id) ? 'text-red-600' : 'text-blue-600')}>
            {stepper.current.title}
          </p>
        </div>
      </div>

      {/* Desktop Stepper */}
      <div className="hidden md:block">
        <div className="relative">
          {/* Steps */}
          <div className="relative flex justify-between items-start">
            {stepper.all.map((step, index) => {
              const isActive = index === currentStepIndex;
              const isCompleted = index < currentStepIndex;
              const hasErrors = getStepErrors(step.id);

              return (
                <div
                  key={step.id}
                  className="flex flex-col items-center cursor-pointer group relative"
                  onClick={() => stepper.goTo(step.id)}
                  style={{ width: `${100 / totalSteps}%` }}
                >
                  {/* Connection Line to Next Step */}
                  {index < stepper.all.length - 1 && (
                    <div
                      className={cn(
                        'absolute top-5 left-1/2 h-0.5 transition-all duration-300',
                        isCompleted && !hasErrors ? 'bg-green-400' : 'bg-gray-200',
                      )}
                      style={{
                        width: `calc(100% - 20px)`,
                        left: 'calc(50% + 20px)',
                        zIndex: 1,
                      }}
                    />
                  )}

                  {/* Step Circle */}
                  <div
                    className={cn(
                      'w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium relative z-10 transition-all duration-200 group-hover:scale-110',
                      hasErrors
                        ? 'bg-red-500 text-white shadow-lg'
                        : isCompleted
                          ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg'
                          : isActive
                            ? 'bg-blue-500 text-white shadow-lg'
                            : 'bg-white border-2 border-gray-300 text-gray-600 group-hover:border-blue-300',
                    )}
                  >
                    {hasErrors ? (
                      <AlertCircle className="w-5 h-5" />
                    ) : isCompleted ? (
                      <Check className="w-5 h-5" />
                    ) : (
                      <span className="text-xs font-semibold">{index + 1}</span>
                    )}
                  </div>

                  {/* Step Label */}
                  <div className="mt-3 text-center px-1">
                    <p
                      className={cn(
                        'text-sm font-medium leading-tight',
                        hasErrors
                          ? 'text-red-600'
                          : isActive
                            ? 'text-blue-600'
                            : isCompleted
                              ? 'text-green-600'
                              : 'text-gray-500 group-hover:text-gray-700',
                      )}
                    >
                      {step.title}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};
