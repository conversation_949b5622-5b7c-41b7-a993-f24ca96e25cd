import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { ArchivedBadge } from '@/components/composite/archived-badge';
import { SopStatusBadge } from '@/components/composite/sop-status-badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { useAnalytics } from '@/hooks/use-analytics';
import { usePermissions } from '@/hooks/use-permissions';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { RouterOutputs } from '@shared/types/router.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { format } from 'date-fns';
import { Archive, Calendar, Copy, Eye, MapPin, MoreHorizontal, Pencil, Trash2, Users } from 'lucide-react';
import { useState } from 'react';
import { useLocation } from 'wouter';

export const SopMobileView = ({ sops }: { sops: RouterOutputs['sop']['list']['result'] }) => {
  const [_, navigate] = useLocation();
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedSop, setSelectedSop] = useState<RouterOutputs['sop']['list']['result'][number] | null>(null);

  const analytics = useAnalytics();
  const { hasPermission } = usePermissions();
  const hasEditPermission = hasPermission(MODULES.SOP, ALLOWED_ACTIONS.EDIT);

  const handleRowAction = (sopId: string, action: 'View' | 'Edit' | 'Archive' | 'Duplicate') => {
    analytics.track(ANALYTICS_EVENTS.SOP.ROW_ACTION_CLICKED, {
      sop_id: sopId,
      action,
    });
  };

  const handleArchiveClick = (e: React.MouseEvent, sop: RouterOutputs['sop']['list']['result'][number]) => {
    e.stopPropagation();
    handleRowAction(sop.instanceId!, 'Archive');
    setSelectedSop(sop);
    setShowArchiveConfirm(true);
  };

  const handleViewClick = (e: React.MouseEvent, sop: RouterOutputs['sop']['list']['result'][number]) => {
    e.stopPropagation();
    handleRowAction(sop.instanceId!, 'View');
    navigate(ROUTES.BUILD_SOP_DETAILS_PATH(sop.instanceId!));
  };

  const handleEditClick = (e: React.MouseEvent, sop: RouterOutputs['sop']['list']['result'][number]) => {
    e.stopPropagation();
    handleRowAction(sop.instanceId!, 'Edit');
    navigate(ROUTES.BUILD_SOP_EDIT_PATH(sop.instanceId!));
  };

  const handleDuplicateClick = (e: React.MouseEvent, sop: RouterOutputs['sop']['list']['result'][number]) => {
    e.stopPropagation();
    handleRowAction(sop.instanceId!, 'Duplicate');
    navigate(ROUTES.BUILD_SOP_DUPLICATE_PATH(sop.instanceId!));
  };

  return (
    <div className="space-y-3">
      {selectedSop && (
        <ArchiveConfirmationDialog
          archived={!!selectedSop.archivedAt}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedSop.instanceId!}
          entityType="sop"
        />
      )}
      {sops.map((sop) => {
        const archived = !!sop.archivedAt;
        return (
          <Card
            key={sop.instanceId}
            className={`cursor-pointer transition-colors hover:bg-muted/50 ${
              archived ? 'bg-amber-50/50 border-amber-200' : ''
            }`}
            onClick={(e) => handleViewClick(e, sop)}
          >
            <CardContent className="p-4">
              {/* Header with ID, Status, and Actions */}
              <div className="flex justify-between items-center mb-3">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">{sop.slug}</span>
                  <SopStatusBadge status={sop.status} />
                  {archived && <ArchivedBadge />}
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild className="flex items-center gap-2">
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={(e) => handleViewClick(e, sop)}>
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </DropdownMenuItem>
                    {!archived && hasEditPermission && (
                      <DropdownMenuItem onClick={(e) => handleEditClick(e, sop)}>
                        <Pencil className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem onClick={(e) => handleDuplicateClick(e, sop)}>
                      <Copy className="h-4 w-4 mr-2" />
                      Duplicate
                    </DropdownMenuItem>
                    <Separator className="my-2" />

                    <DropdownMenuItem
                      className={archived ? 'text-amber-600' : 'text-red-600'}
                      onClick={(e) => handleArchiveClick(e, sop)}
                    >
                      {archived ? (
                        <>
                          <Archive className="h-4 w-4 mr-2 text-amber-600" />
                          Unarchive
                        </>
                      ) : (
                        <>
                          <Trash2 className="h-4 w-4 mr-2 text-red-600" />
                          Archive
                        </>
                      )}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Title */}
              <h3 className="font-medium text-base mb-3 line-clamp-2">{sop.title}</h3>

              {/* Details Grid */}
              <div className="space-y-2 text-sm">
                {/* Owner */}
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-2 text-muted-foreground flex-shrink-0" />
                  <span className="text-muted-foreground min-w-[80px]">Owner:</span>
                  <span className="truncate">{sop?.owner?.fullName || 'Unknown User'}</span>
                </div>

                {/* Location */}
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2 text-muted-foreground flex-shrink-0" />
                  <span className="text-muted-foreground min-w-[80px]">Location:</span>
                  <span className="truncate">{sop.location?.name || 'No location'}</span>
                </div>

                {/* Review Date */}
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-muted-foreground flex-shrink-0" />
                  <span className="text-muted-foreground min-w-[80px]">Review Due:</span>
                  <span>{sop.reviewDate ? format(new Date(sop.reviewDate), 'MMM d, yyyy') : 'No review date'}</span>
                </div>

                {/* Last Revised */}
                <div className="flex items-center justify-between pt-1 text-xs text-muted-foreground">
                  <span>
                    Last revised: {sop.lastRevised ? format(new Date(sop.lastRevised), 'MMM d, yyyy') : 'Never'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};
