import { SopStatusBadge } from '@/components/composite/sop-status-badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { Archive, Calendar, Copy, Eye, HardHat, MapPin, MoreHorizontal, Pencil, Trash2, User } from 'lucide-react';
import { format } from 'date-fns';
import { useLocation } from 'wouter';
import { useState } from 'react';
import { RouterOutputs } from '@shared/types/router.types';
import { ArchiveConfirmationDialog } from '@/components/composite/archive-confirmation-dialog';
import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { useAnalytics } from '@/hooks/use-analytics';
import { ArchivedBadge } from '@/components/composite/archived-badge';
import { TooltipContent, TooltipTrigger, Tooltip, TooltipProvider } from '@/components/ui/tooltip';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { Separator } from '@/components/ui/separator';
import { usePermissions } from '@/hooks/use-permissions';

export const SopTable = ({ sops }: { sops: RouterOutputs['sop']['list']['result'] }) => {
  const [_, navigate] = useLocation();
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [selectedSop, setSelectedSop] = useState<RouterOutputs['sop']['list']['result'][number] | null>(null);
  const analytics = useAnalytics();
  const { hasPermission } = usePermissions();
  const hasEditPermission = hasPermission(MODULES.SOP, ALLOWED_ACTIONS.EDIT);

  const handleRowAction = (sopInstanceId: string, action: 'View' | 'Edit' | 'Archive' | 'Duplicate') => {
    analytics.track(ANALYTICS_EVENTS.SOP.ROW_ACTION_CLICKED, {
      sop_id: sopInstanceId,
      action,
    });
  };

  const handleArchiveClick = (e: React.MouseEvent, sop: RouterOutputs['sop']['list']['result'][number]) => {
    e.stopPropagation();
    handleRowAction(sop.instanceId!, 'Archive');
    setSelectedSop(sop);
    setShowArchiveConfirm(true);
  };

  const handleViewClick = (e: React.MouseEvent, sop: RouterOutputs['sop']['list']['result'][number]) => {
    e.stopPropagation();
    handleRowAction(sop.instanceId!, 'View');
    navigate(ROUTES.BUILD_SOP_DETAILS_PATH(sop.instanceId!));
  };

  const handleEditClick = (e: React.MouseEvent, sop: RouterOutputs['sop']['list']['result'][number]) => {
    e.stopPropagation();
    handleRowAction(sop.instanceId!, 'Edit');
    navigate(ROUTES.BUILD_SOP_EDIT_PATH(sop.instanceId!));
  };

  const handleDuplicateClick = (e: React.MouseEvent, sop: RouterOutputs['sop']['list']['result'][number]) => {
    e.stopPropagation();
    handleRowAction(sop.instanceId!, 'Duplicate');
    navigate(ROUTES.BUILD_SOP_DUPLICATE_PATH(sop.instanceId!));
  };

  return (
    <div className="overflow-hidden">
      {selectedSop && (
        <ArchiveConfirmationDialog
          archived={!!selectedSop.archivedAt}
          showDialog={showArchiveConfirm}
          setShowDialog={setShowArchiveConfirm}
          entityId={selectedSop.instanceId!}
          entityType="sop"
        />
      )}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="max-w-80">SOP</TableHead>
            <TableHead className="w-36">Approver</TableHead>
            <TableHead className="w-28">Status</TableHead>
            <TableHead className="w-40">Next Review Due</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Asset</TableHead>
            <TableHead className="w-36">Last Revised</TableHead>
            <TableHead className="w-32 text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sops?.map((sop) => {
            const archived = !!sop.archivedAt;

            return (
              <TableRow
                key={sop.instanceId}
                className={`cursor-pointer ${archived ? 'bg-amber-50/50 hover:bg-amber-50/80' : ''}`}
                onClick={(e) => {
                  handleViewClick(e, sop);
                }}
              >
                <TableCell className="max-w-80">
                  <div className="flex flex-col">
                    <div className="flex items-center gap-1">
                      <div className="font-medium truncate">{sop.slug}</div>
                      {archived && <ArchivedBadge />}
                    </div>
                    <div className="text-sm text-muted-foreground line-clamp-1 truncate">{sop.title}</div>
                  </div>
                </TableCell>
                <TableCell className="w-36">
                  <div className="flex items-center">
                    <User className="h-3 w-3 mr-1 text-gray-400" />
                    <span className={`truncate ${!sop?.owner?.fullName ? 'text-gray-400' : ''}`}>
                      {sop?.owner?.fullName || 'Unknown User'}
                    </span>
                  </div>
                </TableCell>
                <TableCell className="w-28">
                  <SopStatusBadge status={sop.status} />
                </TableCell>
                <TableCell className="w-40">
                  {sop.reviewDate ? (
                    <div className="flex items-center">
                      <Calendar className="h-3 w-3 mr-1 text-gray-400" />
                      {format(new Date(sop.reviewDate), 'MMM d, yyyy')}
                    </div>
                  ) : (
                    <span className="text-gray-400">No review date</span>
                  )}
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center">
                          <MapPin className="h-3 w-3 mr-1 text-gray-400" />
                          <span className={`truncate ${!sop.location?.name && 'text-muted-foreground'}`}>
                            {sop.location?.name || 'No location'}
                          </span>
                        </div>
                      </TooltipTrigger>
                      {sop.location && (
                        <TooltipContent>
                          <p>{sop.location?.name}</p>
                        </TooltipContent>
                      )}
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <HardHat className="h-3 w-3 mr-1 text-gray-400" />
                    {sop.assets && sop.assets.length > 0 ? (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="truncate">
                              {sop.assets.length === 1
                                ? sop.assets[0].name
                                : `${sop.assets[0].name} +${sop.assets.length - 1} more`}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="max-w-xs">
                              {sop.assets.map((asset, index) => (
                                <p key={asset.id}>
                                  {index > 0 && ', '}
                                  {asset.name}
                                </p>
                              ))}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ) : (
                      <span className="text-muted-foreground">No assets</span>
                    )}
                  </div>
                </TableCell>
                <TableCell className="w-36">
                  {sop.lastRevised ? (
                    format(new Date(sop.lastRevised), 'MMM d, yyyy')
                  ) : (
                    <span className="text-gray-400">No revision date</span>
                  )}
                </TableCell>
                <TableCell className="w-32 text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={(e) => handleViewClick(e, sop)}>
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </DropdownMenuItem>
                      {!archived && hasEditPermission && (
                        <DropdownMenuItem onClick={(e) => handleEditClick(e, sop)}>
                          <Pencil className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem onClick={(e) => handleDuplicateClick(e, sop)}>
                        <Copy className="h-4 w-4 mr-2" />
                        Duplicate
                      </DropdownMenuItem>
                      <Separator className="my-2" />

                      <DropdownMenuItem
                        className={archived ? 'text-amber-600' : 'text-red-600'}
                        onClick={(e) => handleArchiveClick(e, sop)}
                      >
                        {archived ? (
                          <>
                            <Archive className="h-4 w-4 mr-2 text-amber-600" />
                            Unarchive
                          </>
                        ) : (
                          <>
                            <Trash2 className="h-4 w-4 mr-2 text-red-600" />
                            Archive
                          </>
                        )}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};
