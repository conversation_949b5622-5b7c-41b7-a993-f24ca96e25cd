import { AiCompose } from '@/components/composite/ai-compose';
import { VoiceInputRef } from '@/components/composite/voice-input';
import { CreateSopType } from '@shared/types/sop.types';
import { useRef } from 'react';
import { useFormContext } from 'react-hook-form';
import { FileEdit } from 'lucide-react';

export const StartStep = ({ mode }: { mode: 'create' | 'edit' }) => {
  const form = useFormContext<CreateSopType>();
  const voiceInputRef = useRef<VoiceInputRef>(null);

  const onAnalysisComplete = (sopData: CreateSopType) => {
    form.reset(sopData);
  };

  if (mode === 'edit') {
    return (
      <div className="space-y-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex items-start space-x-3">
              <FileEdit className="w-6 h-6 text-blue-500 mt-0.5" />
              <div>
                <h3 className="font-medium text-blue-900 mb-2">Editing Existing SOP</h3>
                <p className="text-blue-700">
                  Your existing SOP data has been loaded. Use the step navigation above to review and edit different
                  sections.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="max-w-4xl mx-auto">
        <AiCompose
          methods={['voice', 'document']}
          entity="sop"
          onVoiceComplete={onAnalysisComplete}
          onDocumentComplete={onAnalysisComplete}
          voiceInputRef={voiceInputRef}
        />
      </div>
    </div>
  );
};
