import { Button } from '@/components/ui/button';
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { CreateSopType, SOP_SECTION_LABELS } from '@shared/types/sop.types';
import { GripVertical, Plus, Trash2 } from 'lucide-react';
import { useFieldArray, useFormContext } from 'react-hook-form';

type SortableEmergencyFieldProps = {
  id: string;
  sectionIndex: number;
  onRemove: () => void;
  canRemove: boolean;
  label: string;
};

const SortableEmergencyField = ({ id, sectionIndex, onRemove, canRemove, label }: SortableEmergencyFieldProps) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id });
  const form = useFormContext<CreateSopType>();

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn('border border-red-200 rounded-lg bg-red-50/30', isDragging && 'shadow-lg z-50 bg-white')}
    >
      <div className="flex items-center justify-between p-4 bg-red-100/50 border-b border-red-200">
        <div className="flex items-center gap-2">
          <Button
            type="button"
            variant="ghost"
            size="icon"
            {...attributes}
            {...listeners}
            className="cursor-grab active:cursor-grabbing size-6"
          >
            <GripVertical className="h-4 w-4 text-gray-400" />
          </Button>
          <h4 className="font-medium text-gray-900">{label}</h4>
        </div>

        {canRemove && (
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={onRemove}
            className="text-red-600 hover:text-red-700 hover:bg-red-100 size-6"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>

      <div className="p-4">
        <FormField
          control={form.control}
          name={`sections.${sectionIndex}.value`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Textarea
                  placeholder={`Enter details for ${label.toLowerCase()}...`}
                  className="min-h-[120px] bg-white"
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};

export const EmergencyPlan = () => {
  const form = useFormContext<CreateSopType>();

  const {
    fields: allSections,
    append,
    remove,
    move,
  } = useFieldArray({
    control: form.control,
    name: 'sections',
    keyName: 'fieldId',
  });

  // Filter emergency sections
  const emergencySections = allSections
    .map((section, index) => ({ ...section, originalIndex: index }))
    .filter((section) => form.watch(`sections.${section.originalIndex}.sectionType`) === 'emergency');

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const addEmergencyField = () => {
    const currentEmergencySections = form.watch('sections').filter((s) => s.sectionType === 'emergency');
    const nextSerial = Math.max(0, ...currentEmergencySections.map((s) => s.serial)) + 1;

    // Default to first available emergency label or create custom
    const defaultLabel =
      SOP_SECTION_LABELS.emergency.find(
        (label) => !currentEmergencySections.some((section) => section.label === label),
      ) || 'Custom Emergency Field';

    append({
      sectionType: 'emergency',
      serial: nextSerial,
      value: '',
      label: defaultLabel,
    });
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = emergencySections.findIndex((section) => section.fieldId === active.id);
      const newIndex = emergencySections.findIndex((section) => section.fieldId === over?.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        const oldOriginalIndex = emergencySections[oldIndex].originalIndex;
        const newOriginalIndex = emergencySections[newIndex].originalIndex;
        move(oldOriginalIndex, newOriginalIndex);
      }
    }
  };

  // Initialize with default emergency fields if none exist
  const initializeDefaultFields = () => {
    const defaultFields = [
      'Potential Emergencies',
      'Emergency Contacts',
      'Evacuation Procedures',
      'First Aid Response',
      'Incident Reporting Process',
    ];

    defaultFields.forEach((label, index) => {
      append({
        sectionType: 'emergency',
        serial: index + 1,
        value: '',
        label: label,
      });
    });
  };

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">Emergency Plan</h2>
        <p className="text-gray-600">
          Define emergency procedures and response protocols. Each field represents a critical aspect of emergency
          preparedness for this SOP.
        </p>
      </div>

      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Emergency Fields</h3>
        <Button type="button" onClick={addEmergencyField}>
          <Plus className="h-4 w-4 mr-2" />
          Add Emergency Field
        </Button>
      </div>

      {emergencySections.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
          <div className="space-y-4">
            <p className="text-gray-500">No emergency procedures defined yet</p>
            <Button type="button" onClick={initializeDefaultFields}>
              <Plus className="h-4 w-4 mr-2" />
              Add Default Emergency Fields
            </Button>
          </div>
        </div>
      ) : (
        <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
          <SortableContext
            items={emergencySections.map((section) => section.fieldId)}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-4">
              {emergencySections.map((section) => {
                const sectionData = form.watch(`sections.${section.originalIndex}`);
                return (
                  <SortableEmergencyField
                    key={section.fieldId}
                    id={section.fieldId}
                    sectionIndex={section.originalIndex}
                    onRemove={() => remove(section.originalIndex)}
                    canRemove={emergencySections.length > 1}
                    label={sectionData?.label || 'Emergency Field'}
                  />
                );
              })}
            </div>
          </SortableContext>
        </DndContext>
      )}
    </div>
  );
};
