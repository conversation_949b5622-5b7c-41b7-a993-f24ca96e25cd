import { Button } from '@/components/ui/button';
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { sopSectionTypeEnum } from '@shared/schema';
import { CreateSopType } from '@shared/types/sop.types';
import { GripVertical, Plus, Trash2 } from 'lucide-react';
import { useFieldArray, useFormContext } from 'react-hook-form';

type SortableProcedureItemProps = {
  id: string;
  index: number;
  sectionIndex: number;
  onRemove: () => void;
  canRemove: boolean;
};

const SortableProcedureItem = ({ id, index, sectionIndex, onRemove, canRemove }: SortableProcedureItemProps) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id });
  const form = useFormContext<CreateSopType>();

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn('flex items-start gap-3 p-4 bg-white border rounded-lg', isDragging && 'shadow-lg z-50')}
    >
      <div className="flex items-center gap-2 mt-1">
        <span className="text-sm font-medium text-blue-600 bg-blue-50 rounded-full w-6 h-6 flex items-center justify-center">
          {index + 1}
        </span>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          {...attributes}
          {...listeners}
          className="cursor-grab active:cursor-grabbing size-5"
        >
          <GripVertical className="h-4 w-4 text-gray-400" />
        </Button>
      </div>

      <div className="flex-1 space-y-3">
        <FormField
          control={form.control}
          name={`sections.${sectionIndex}.label`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input placeholder="Step title..." className="font-medium" {...field} value={field.value || ''} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name={`sections.${sectionIndex}.value`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Textarea
                  placeholder="Detailed step instructions..."
                  className="min-h-[80px]"
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <Button
        type="button"
        variant="ghost"
        size="icon"
        onClick={onRemove}
        disabled={!canRemove}
        className="text-red-600 hover:text-red-700 hover:bg-red-50 size-8 mt-1"
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
};

type ProcedureSectionProps = {
  title: string;
  description: string;
  sectionType: (typeof sopSectionTypeEnum.enumValues)[number];
  colorClass: string;
  buttonText: string;
};

const ProcedureSection = ({ title, description, sectionType, colorClass, buttonText }: ProcedureSectionProps) => {
  const form = useFormContext<CreateSopType>();

  const {
    fields: allSections,
    append,
    remove,
    move,
  } = useFieldArray({
    control: form.control,
    name: 'sections',
    keyName: 'fieldId',
  });

  // Filter sections for this specific type
  const typeSections = allSections
    .map((section, index) => ({ ...section, originalIndex: index }))
    .filter((section) => form.watch(`sections.${section.originalIndex}.sectionType`) === sectionType);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const addSection = () => {
    const currentTypeSections = form.watch('sections').filter((s) => s.sectionType === sectionType);
    const nextSerial = Math.max(0, ...currentTypeSections.map((s) => s.serial)) + 1;

    append({
      sectionType,
      serial: nextSerial,
      value: '',
      label: '',
    });
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = typeSections.findIndex((section) => section.fieldId === active.id);
      const newIndex = typeSections.findIndex((section) => section.fieldId === over?.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        const oldOriginalIndex = typeSections[oldIndex].originalIndex;
        const newOriginalIndex = typeSections[newIndex].originalIndex;
        move(oldOriginalIndex, newOriginalIndex);
      }
    }
  };

  return (
    <div className={`border rounded-lg ${colorClass}`}>
      <div className="p-6 border-b">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">{title}</h3>
            <p className="text-sm text-gray-600">{description}</p>
          </div>
          <Button type="button" variant="outline" size="sm" onClick={addSection}>
            <Plus className="h-4 w-4 mr-1" />
            {buttonText}
          </Button>
        </div>
      </div>

      <div className="p-6">
        {typeSections.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p className="text-sm">No {title.toLowerCase()} added yet</p>
            <Button type="button" variant="ghost" onClick={addSection} className="mt-2">
              <Plus className="h-4 w-4 mr-1" />
              Add First Item
            </Button>
          </div>
        ) : (
          <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
            <SortableContext
              items={typeSections.map((section) => section.fieldId)}
              strategy={verticalListSortingStrategy}
            >
              <div className="space-y-3">
                {typeSections.map((section, index) => (
                  <SortableProcedureItem
                    key={section.fieldId}
                    id={section.fieldId}
                    index={index}
                    sectionIndex={section.originalIndex}
                    onRemove={() => remove(section.originalIndex)}
                    canRemove={typeSections.length > 1}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        )}
      </div>
    </div>
  );
};

export const ProcedureSteps = () => {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-2xl font-semibold text-gray-900 mb-2">Procedure Steps</h2>
        <p className="text-gray-600">
          Define comprehensive step-by-step instructions with pre and post procedure requirements.
        </p>
      </div>

      <div className="space-y-6">
        {/* Pre-Procedure Requirements */}
        <ProcedureSection
          title="Pre-Procedure Requirements"
          description="Actions that must be completed before starting the main procedure"
          sectionType="pre_procedure"
          colorClass="border-blue-200 bg-blue-50/30"
          buttonText="Add Requirement"
        />

        {/* Main Procedure Steps */}
        <ProcedureSection
          title="Procedure Steps"
          description="Core step-by-step instructions for completing the procedure"
          sectionType="procedure"
          colorClass="border-green-200 bg-green-50/30"
          buttonText="Add Step"
        />

        {/* Post-Procedure Requirements */}
        <ProcedureSection
          title="Post-Procedure Requirements"
          description="Actions that must be taken after completing the main procedure"
          sectionType="post_procedure"
          colorClass="border-purple-200 bg-purple-50/30"
          buttonText="Add Requirement"
        />
      </div>
    </div>
  );
};
