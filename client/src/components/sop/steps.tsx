import { EmergencyPlan } from '@/components/sop/upsert/emergency-plan';
import { GeneralInformationStep } from '@/components/sop/upsert/general-information-step';
import { HazardsAndControlsStep } from '@/components/sop/upsert/hazards-and-controls-step';
import { MetadataStep } from '@/components/sop/upsert/metadata-step';
import { ProcedureSteps } from '@/components/sop/upsert/procedure-steps';
import { StartStep } from '@/components/sop/upsert/start-step';
import { useStepper } from '@/components/sop/stepper';

export const SopSteps = ({ mode }: { mode: 'create' | 'edit' }) => {
  const stepper = useStepper();

  return (
    <div>
      {stepper.switch({
        start: () => <StartStep mode={mode} />,
        'general-information': () => <GeneralInformationStep />,
        'hazards-and-control': () => <HazardsAndControlsStep />,
        'procedure-steps': () => <ProcedureSteps />,
        'emergency-plans': () => <EmergencyPlan />,
        metadata: () => <MetadataStep />,
      })}
    </div>
  );
};
