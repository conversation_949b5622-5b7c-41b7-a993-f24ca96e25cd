import { ANALYTICS_EVENTS } from '@/analytics/event-names';
import { Button } from '@/components/ui/button';
import { useAnalytics } from '@/hooks/use-analytics';
import { trpc } from '@/providers/trpc';
import { RouterOutputs } from '@shared/types/router.types';
import { ArrowLeft, FileText, Mic, Video } from 'lucide-react';
import { useState, useRef } from 'react';
import { toast } from 'sonner';
import { DocumentInput } from './document-input';
import { VideoInput } from './video-input';
import { VoiceInput, VoiceInputRef } from './voice-input';

// Type-safe method definitions
export type AiComposeMethod = 'voice' | 'document' | 'video';

// Supported entity types for internal endpoint mapping
export type AiComposeEntity = 'event' | 'capa' | 'jha' | 'sop';

// Infer proper types from the schemas
type JhaAnalysisResult = RouterOutputs['ai']['analyzeJha'];
type EventAnalysisResult = RouterOutputs['ai']['analyzeEvent'];
type CapaAnalysisResult = RouterOutputs['ai']['analyzeCapa'];
type SopAnalysisResult = RouterOutputs['ai']['analyzeSop'];

// Voice analysis result types based on entity
type VoiceAnalysisResult<T extends AiComposeEntity> = T extends 'jha'
  ? JhaAnalysisResult
  : T extends 'sop'
    ? SopAnalysisResult // TODO: Update when SOP schema is available
    : T extends 'capa'
      ? CapaAnalysisResult // TODO: Update when CAPA schema is available
      : T extends 'event'
        ? EventAnalysisResult // TODO: Update when event schema is available
        : never;

// Document analysis result types based on entity
type DocumentAnalysisResult<T extends AiComposeEntity> = T extends 'jha'
  ? JhaAnalysisResult
  : T extends 'sop'
    ? SopAnalysisResult // TODO: Update when SOP schema is available
    : T extends 'capa'
      ? CapaAnalysisResult // TODO: Update when CAPA schema is available
      : T extends 'event'
        ? EventAnalysisResult // TODO: Update when event schema is available
        : never;

// Video analysis result types based on entity (placeholder for future)
type VideoAnalysisResult<T extends AiComposeEntity> = T extends 'jha'
  ? JhaAnalysisResult
  : T extends 'sop'
    ? SopAnalysisResult // TODO: Update when SOP schema is available
    : T extends 'capa'
      ? CapaAnalysisResult // TODO: Update when CAPA schema is available
      : T extends 'event'
        ? EventAnalysisResult // TODO: Update when event schema is available
        : never;

export type AiComposeProps<T extends AiComposeEntity = AiComposeEntity> = {
  /** Array of available input methods */
  methods: AiComposeMethod[];
  /** Entity type for internal endpoint mapping */
  entity: T;
  /** Callback for voice analysis completion */
  onVoiceComplete?: (data: VoiceAnalysisResult<T>, mode: 'voice' | 'text') => void;
  /** Callback for document analysis completion */
  onDocumentComplete?: (data: DocumentAnalysisResult<T>) => void;
  /** Callback for video analysis completion */
  onVideoComplete?: (data: VideoAnalysisResult<T>) => void;
  /** Whether any analysis is currently in progress */
  isLoading?: boolean;
  voiceInputRef?: React.RefObject<VoiceInputRef | null>;
};

type CreationMethod = AiComposeMethod | 'select';

// Method configuration for UI display
const METHOD_CONFIG = {
  voice: {
    icon: Mic,
    title: 'Describe with AI',
    description: 'Use your voice or text to describe the procedure. Our AI will help fill in the fields.',
    color: 'blue' as const,
  },
  document: {
    icon: FileText,
    title: 'Import from Document',
    description: 'Upload an existing PDF document. Our AI will parse it and pre-fill the wizard.',
    color: 'green' as const,
  },
  video: {
    icon: Video,
    title: 'Create from Video',
    description: 'Upload a video demonstration. Our AI will extract steps and procedures.',
    color: 'purple' as const,
  },
} as const;

export const AiCompose = <T extends AiComposeEntity>({
  methods,
  entity,
  onVoiceComplete,
  onDocumentComplete,
  onVideoComplete: _onVideoComplete,
  isLoading = false,
  voiceInputRef,
}: AiComposeProps<T>) => {
  const [currentMethod, setCurrentMethod] = useState<CreationMethod>(methods.length === 1 ? methods[0] : 'select');

  // Analytics tracking
  const analytics = useAnalytics();
  const voiceStartTimeRef = useRef<number | null>(null);
  const inputModeRef = useRef<'voice' | 'text'>('voice');

  // Get analytics events based on entity type
  const getAnalyticsEvents = () => {
    switch (entity) {
      case 'jha':
        return ANALYTICS_EVENTS.JHA;
      case 'sop':
        return ANALYTICS_EVENTS.SOP;
      case 'capa':
        return ANALYTICS_EVENTS.CAPA;
      case 'event':
        return ANALYTICS_EVENTS.EVENT;
      default:
        return ANALYTICS_EVENTS.EVENT;
    }
  };

  // Count populated fields from analysis result
  const countPopulatedFields = (data: Record<string, unknown>): number => {
    if (!data) return 0;

    let count = 0;
    const fieldsToCheck = Object.keys(data);

    for (const field of fieldsToCheck) {
      const value = data[field];
      if (value !== null && value !== undefined && value !== '') {
        // Handle arrays and objects
        if (Array.isArray(value)) {
          if (value.length > 0) count++;
        } else if (typeof value === 'object') {
          // For nested objects, count if they have any non-empty values
          if (Object.values(value as Record<string, unknown>).some((v) => v !== null && v !== undefined && v !== '')) {
            count++;
          }
        } else {
          count++;
        }
      }
    }

    return count;
  };

  // Get appropriate tRPC mutations based on entity type
  const getVoiceMutation = () => {
    switch (entity) {
      case 'jha':
        return trpc.ai.analyzeJha;
      case 'sop':
        return trpc.ai.analyzeSop;
      case 'capa':
        return trpc.ai.analyzeCapa;
      case 'event':
        return trpc.ai.analyzeEvent;
      default:
        return trpc.ai.analyzeJha;
    }
  };

  const getDocumentMutation = () => {
    switch (entity) {
      case 'jha':
        return trpc.ai.analyzeJhaDocument;
      case 'sop':
        return trpc.ai.analyzeSopDocument;
      default:
        return trpc.ai.analyzeJhaDocument;
    }
  };

  const { mutateAsync: analyzeVoice, isPending: isAnalyzingVoice } = getVoiceMutation().useMutation({
    onSuccess: (data) => {
      if (data) {
        const endTime = Date.now();
        const duration = voiceStartTimeRef.current ? endTime - voiceStartTimeRef.current : 0;
        const fieldsPopulated = countPopulatedFields(data as Record<string, unknown>);
        const events = getAnalyticsEvents();

        // Track successful voice analysis
        analytics.track(events.VOICE_SUCCESSFUL, {
          duration_ms: duration,
          fields_populated_count: fieldsPopulated,
        });

        // Track individual field population for analytics
        if (data && typeof data === 'object') {
          const dataRecord = data as Record<string, unknown>;
          Object.keys(dataRecord).forEach((fieldName) => {
            const value = dataRecord[fieldName];
            if (value !== null && value !== undefined && value !== '') {
              analytics.track(events.FIELD_AI_POPULATED, {
                field_name: fieldName,
              });
            }
          });
        }

        const isVoiceMode = inputModeRef.current === 'voice';
        toast.success(isVoiceMode ? 'Voice Parsed Successfully' : 'Text Analyzed Successfully', {
          description: isVoiceMode
            ? 'Your voice has been analyzed and processed.'
            : 'Your text has been analyzed and processed.',
        });
        onVoiceComplete?.(data as VoiceAnalysisResult<T>, inputModeRef.current);
      }
    },
    onError: (error) => {
      console.error('Voice parsing error:', error);

      const endTime = Date.now();
      const duration = voiceStartTimeRef.current ? endTime - voiceStartTimeRef.current : 0;
      const events = getAnalyticsEvents();

      // Determine failure reason
      let reason: 'no_speech_detected' | 'mic_denied' | 'api_error' = 'api_error';
      if (error instanceof Error) {
        if (error.message.includes('speech') || error.message.includes('audio')) {
          reason = 'no_speech_detected';
        } else if (error.message.includes('permission') || error.message.includes('microphone')) {
          reason = 'mic_denied';
        }
      }

      // Track failed voice analysis
      analytics.track(events.VOICE_FAILED, {
        reason,
        duration_ms: duration,
      });

      const isVoiceMode = inputModeRef.current === 'voice';
      toast.error(isVoiceMode ? 'Voice Parsing Error' : 'Text Analysis Error', {
        description:
          error.message ||
          (isVoiceMode
            ? 'Failed to parse the voice. Please try again.'
            : 'Failed to analyze the text. Please try again.'),
      });
    },
  });

  const { mutateAsync: analyzeDocument, isPending: isAnalyzingDocument } = getDocumentMutation().useMutation({
    onSuccess: (data) => {
      if (data) {
        toast.success('Document Parsed Successfully', {
          description: 'Your document has been analyzed and processed.',
        });
        onDocumentComplete?.(data as DocumentAnalysisResult<T>);
      }
    },
    onError: (error) => {
      console.error('Document parsing error:', error);
      toast.error('Document Parsing Error', {
        description: error.message || 'Failed to parse the document. Please try again.',
      });
    },
  });

  const handleVoiceAnalysis = async (text: string, mode: 'voice' | 'text'): Promise<void> => {
    const events = getAnalyticsEvents();

    // Store the input mode for use in mutation callbacks
    inputModeRef.current = mode;

    // Track voice analysis started
    voiceStartTimeRef.current = Date.now();
    analytics.track(events.VOICE_STARTED, {});

    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    await analyzeVoice({ text, timezone });
  };

  const handleDocumentAnalysis = async (documentBase64: string, filename: string): Promise<void> => {
    await analyzeDocument({
      documentBase64,
      filename,
      mediaType: 'application/pdf' as const,
    });
  };

  const handleVideoAnalysis = async (videoBase64: string, filename: string): Promise<void> => {
    // TODO: Implement video analysis when endpoint is available
    toast.info('Video Analysis', {
      description: 'Video analysis feature is coming soon!',
    });
    console.log('Video analysis not yet implemented:', {
      filename,
      videoBase64: videoBase64.substring(0, 100) + '...',
    });
  };

  const handleChangeMethod = (): void => {
    setCurrentMethod(methods.length === 1 ? methods[0] : 'select');
  };

  type ColorVariant = 'blue' | 'green' | 'purple';

  const getColorClasses = (color: ColorVariant) => {
    const colorMap = {
      blue: {
        border: 'border-blue-200 hover:border-blue-300',
        bg: 'hover:bg-blue-50/30',
        iconBg: 'bg-blue-100 group-hover:bg-blue-200',
        iconColor: 'text-blue-600',
        buttonColor: 'text-blue-600 hover:text-blue-700',
      },
      green: {
        border: 'border-green-200 hover:border-green-300',
        bg: 'hover:bg-green-50/30',
        iconBg: 'bg-green-100 group-hover:bg-green-200',
        iconColor: 'text-green-600',
        buttonColor: 'text-green-600 hover:text-green-700',
      },
      purple: {
        border: 'border-purple-200 hover:border-purple-300',
        bg: 'hover:bg-purple-50/30',
        iconBg: 'bg-purple-100 group-hover:bg-purple-200',
        iconColor: 'text-purple-600',
        buttonColor: 'text-purple-600 hover:text-purple-700',
      },
    } as const;

    return colorMap[color];
  };

  // Show method selection screen when multiple methods are available
  if (currentMethod === 'select') {
    return (
      <div className="my-4 space-y-4">
        <div className="text-center space-y-2">
          <h2 className="text-xl font-semibold text-gray-900">Choose Your Creation Method</h2>
          <p className="text-gray-600">Select how you'd like to create this {entity.toUpperCase()}</p>
        </div>

        <div className={`grid ${methods.length === 2 ? 'md:grid-cols-2' : 'md:grid-cols-3'} gap-6`}>
          {methods.map((method) => {
            const config = METHOD_CONFIG[method];
            const colors = getColorClasses(config.color);
            const Icon = config.icon;

            return (
              <div key={method} className="group cursor-pointer h-full" onClick={() => setCurrentMethod(method)}>
                <div
                  className={`border-2 border-dashed bg-white ${colors.border} rounded-lg p-8 text-center space-y-4 ${colors.bg} transition-all duration-200 group-hover:shadow-md h-full flex flex-col`}
                >
                  <div
                    className={`mx-auto w-16 h-16 ${colors.iconBg} rounded-full flex items-center justify-center transition-colors`}
                  >
                    <Icon className={`w-8 h-8 ${colors.iconColor}`} />
                  </div>
                  <div className="space-y-2 flex-1 flex flex-col justify-center">
                    <h3 className="text-lg font-medium text-gray-900">{config.title}</h3>
                    <p className="text-sm text-gray-600 leading-relaxed">{config.description}</p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  // Show voice input method
  if (currentMethod === 'voice') {
    const config = METHOD_CONFIG.voice;
    const colors = getColorClasses(config.color);
    const Icon = config.icon;

    return (
      <div className="my-4 space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Icon className={`w-6 h-6 ${colors.iconColor}`} />
              <h2 className="text-xl font-semibold text-gray-900">Speak or type your {entity} description</h2>
            </div>
          </div>
          {methods.length > 1 && (
            <Button variant="ghost" size="sm" onClick={handleChangeMethod} className={colors.buttonColor}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Change Method
            </Button>
          )}
        </div>

        <VoiceInput
          onAnalysisComplete={handleVoiceAnalysis}
          isLoading={isAnalyzingVoice || isLoading}
          ref={voiceInputRef}
        />
      </div>
    );
  }

  // Show document input method
  if (currentMethod === 'document') {
    const config = METHOD_CONFIG.document;
    const colors = getColorClasses(config.color);
    const Icon = config.icon;

    return (
      <div className="my-4 space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Icon className={`w-6 h-6 ${colors.iconColor}`} />
              <h2 className="text-xl font-semibold text-gray-900">Upload & AI Parse Document</h2>
            </div>
          </div>
          {methods.length > 1 && (
            <Button variant="ghost" size="sm" onClick={handleChangeMethod} className={colors.buttonColor}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Change Method
            </Button>
          )}
        </div>

        <p className="text-gray-600">
          Upload an existing {entity.toUpperCase()} document. Our AI will analyze it and pre-fill the fields below.
          Processing usually takes 1-2 minutes.
        </p>

        <DocumentInput onUpload={handleDocumentAnalysis} isParsing={isAnalyzingDocument || isLoading} />
      </div>
    );
  }

  // Show video input method
  if (currentMethod === 'video') {
    const config = METHOD_CONFIG.video;
    const colors = getColorClasses(config.color);
    const Icon = config.icon;

    return (
      <div className="my-4 space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Icon className={`w-6 h-6 ${colors.iconColor}`} />
              <h2 className="text-xl font-semibold text-gray-900">Record or Upload Video</h2>
            </div>
          </div>
          {methods.length > 1 && (
            <Button variant="ghost" size="sm" onClick={handleChangeMethod} className={colors.buttonColor}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Change Method
            </Button>
          )}
        </div>

        <p className="text-gray-600">
          Record a video demonstration or upload an existing video. Our AI will extract steps and procedures.
        </p>

        <VideoInput onUpload={handleVideoAnalysis} isParsing={isLoading} />
      </div>
    );
  }

  return null;
};
