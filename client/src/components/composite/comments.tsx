import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { useAppContext } from '@/contexts/app-context';
import { useInfiniteUsersPublic } from '@/hooks/use-paginated-data';
import { usePermissions } from '@/hooks/use-permissions';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { formatDate } from '@shared/date-utils';
import { statusEnum } from '@shared/schema';
import { CreateCommentFormSchema, CreateCommentFormSchemaValidations } from '@shared/types/comments.types';
import { RouterOutputs } from '@shared/types/router.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { MessageSquare, MoreHorizontal, Send, Trash2 } from 'lucide-react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

// Type definitions
type Comment = RouterOutputs['event']['listComments'][0];

interface TeamMemberMention {
  id: string;
  fullName: string;
}

interface DropdownPosition {
  top: number;
  left: number;
}

// Constants
const DROPDOWN_POSITION = { top: 34, left: 20 }; // FIXME: not ideal when there is content with more than one line/row

const FormSchema = CreateCommentFormSchema.extend(CreateCommentFormSchemaValidations);

export const CommentsSection = ({
  entityId,
  entityType,
  entitySlug,
  entityTitle,
  status,
  entityOwnerId,
}: {
  entityId: string;
  entityType: 'event' | 'capa';
  entitySlug: string;
  entityTitle: string;
  entityOwnerId?: string;
  status: (typeof statusEnum.enumValues)[number];
}) => {
  const { hasPermission } = usePermissions();
  const utils = trpc.useUtils();

  const MODULE_MAP = {
    event: {
      module: MODULES.EHS_EVENT,
      router: trpc.event,
    },
    capa: {
      module: MODULES.EHS_CAPA,
      router: trpc.capa,
    },
  };

  const entityConfig = MODULE_MAP[entityType];
  const canCreateComment = hasPermission(entityConfig.module, ALLOWED_ACTIONS.EDIT, entityOwnerId);

  // tRPC mutations and queries using dynamic entity configuration
  const createCommentMutation = entityConfig.router.createComment.useMutation({
    onSuccess: () => {
      utils.auditTrail.get.invalidate({
        entityId,
        entityType,
      });
      utils[entityType].listComments.invalidate({
        entityId,
        options: { limit: 50 },
      });
    },
  });

  const listCommentsQuery = entityConfig.router.listComments.useQuery({
    entityId,
    options: { limit: 50, offset: 0 },
  });

  const deleteCommentMutation = entityConfig.router.deleteComment.useMutation({
    onSuccess: () => {
      utils[entityType].listComments.invalidate({
        entityId,
        options: { limit: 50 },
      });
    },
  });

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      content: '',
      status,
      entityId,
      entitySlug,
      entityTitle,
      entityType,
    },
    mode: 'onSubmit',
  });

  // State management
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  const [showMentionDropdown, setShowMentionDropdown] = useState(false);
  const [mentionFilter, setMentionFilter] = useState('');
  const [dropdownPosition, setDropdownPosition] = useState<DropdownPosition>(DROPDOWN_POSITION);
  const [selectedMentionIndex, setSelectedMentionIndex] = useState(0);
  const [commentToDelete, setCommentToDelete] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeletingComment, setIsDeletingComment] = useState(false);

  // Refs
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const commentContainerRef = useRef<HTMLDivElement>(null);

  // Context
  const { user } = useAppContext();

  // Use data from our queries
  const fetchedComments = listCommentsQuery.data || [];
  const isLoadingComments = listCommentsQuery.isLoading;

  const { data: users = [] } = useInfiniteUsersPublic({
    upkeepCompanyId: user?.upkeepCompanyId!,
  });

  // Transform users to team members format
  const teamMembers: TeamMemberMention[] = useMemo(() => {
    return users.map((user) => ({
      id: user.id,
      fullName: user.fullName ?? '[user has not accepted invite]',
    }));
  }, [users]);

  // Utility functions
  const getTextContent = useCallback((): string => {
    return textareaRef.current?.value || '';
  }, []);

  const getCursorPosition = useCallback((): number => {
    if (!textareaRef.current) return 0;
    return textareaRef.current.selectionStart || 0;
  }, []);

  const setCursorPosition = useCallback((position: number) => {
    if (!textareaRef.current) return;
    textareaRef.current.setSelectionRange(position, position);
    textareaRef.current.focus();
  }, []);

  const formatMentions = useCallback(
    (text: string): string => {
      // Escape HTML characters
      let formattedText = text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');

      // Replace @userId mentions with blue spans showing the actual name
      teamMembers.forEach((member) => {
        const userIdMentionRegex = new RegExp(`@${member.id}\\b`, 'gi');
        formattedText = formattedText.replace(
          userIdMentionRegex,
          `<span style="color: #3e63dd; font-weight: 500;">@${member.fullName}</span>`,
        );
      });

      return formattedText.replace(/\n/g, '<br>');
    },
    [teamMembers],
  );

  const formatDisplayedMentions = useCallback(
    (content: string) => {
      if (!content || typeof content !== 'string') return content;

      let processedContent = content;

      // Replace @userId mentions with actual names first
      teamMembers.forEach((member) => {
        const userIdMentionRegex = new RegExp(`@${member.id}\\b`, 'gi');
        processedContent = processedContent.replace(userIdMentionRegex, `@${member.fullName}`);
      });

      // Create an array to track all mentions and their positions
      const mentions: Array<{ start: number; end: number; member: TeamMemberMention }> = [];

      // Find all mentions in the processed content
      teamMembers.forEach((member) => {
        const escapedName = member.fullName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const mentionRegex = new RegExp(`@${escapedName}\\b`, 'gi');

        let match;
        while ((match = mentionRegex.exec(processedContent)) !== null) {
          mentions.push({
            start: match.index,
            end: match.index + match[0].length,
            member: member,
          });
        }
      });

      // Sort mentions by start position
      mentions.sort((a, b) => a.start - b.start);

      // Remove overlapping mentions (keep the first one)
      const uniqueMentions = mentions.filter((mention, index) => {
        if (index === 0) return true;
        const prevMention = mentions[index - 1];
        return mention.start >= prevMention.end;
      });

      if (uniqueMentions.length === 0) {
        return processedContent;
      }

      // Build the JSX parts
      const parts: React.ReactNode[] = [];
      let lastIndex = 0;

      uniqueMentions.forEach((mention) => {
        // Add text before the mention
        if (mention.start > lastIndex) {
          parts.push(
            <span key={`text-${lastIndex}-${mention.start}`}>
              {processedContent.substring(lastIndex, mention.start)}
            </span>,
          );
        }

        // Add the highlighted mention
        parts.push(
          <span key={`mention-${mention.start}-${mention.member.id}`} className="text-blue-600 font-medium">
            @{mention.member.fullName}
          </span>,
        );

        lastIndex = mention.end;
      });

      // Add remaining text
      if (lastIndex < processedContent.length) {
        parts.push(<span key={`text-${lastIndex}`}>{processedContent.substring(lastIndex)}</span>);
      }

      return parts;
    },
    [teamMembers],
  );

  // New function to convert displayed content back to stored format (with userIds)
  const convertDisplayedContentToStored = useCallback(
    (displayedText: string): string => {
      let storedText = displayedText;

      // Replace displayed @username mentions back to @userId format
      // Sort by name length (longest first) to avoid partial replacements
      const sortedMembers = [...teamMembers].sort((a, b) => b.fullName.length - a.fullName.length);

      sortedMembers.forEach((member) => {
        const escapedName = member.fullName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const displayNameRegex = new RegExp(`@${escapedName}\\b`, 'gi');
        storedText = storedText.replace(displayNameRegex, `@${member.id}`);
      });

      return storedText;
    },
    [teamMembers],
  );

  // Event handlers
  const handleCommentChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const displayedText = e.target.value;
      const cursorPosition = e.target.selectionStart || 0;

      // Convert displayed content back to stored format (with userIds)
      const storedText = convertDisplayedContentToStored(displayedText);

      // Update form value with stored format
      form.setValue('content', storedText);

      // Trigger form validation if there is text (clears previous error messages)
      if (storedText.trim().length > 0) {
        form.trigger('content');
      }

      if (storedText.trim().length === 0) {
        form.clearErrors('content');
      }

      // @mention detection - look for @ followed by text that could match a team member name
      const beforeCursor = displayedText.substring(0, cursorPosition);
      const lastAtIndex = beforeCursor.lastIndexOf('@');

      if (lastAtIndex !== -1) {
        const textAfterAt = beforeCursor.substring(lastAtIndex + 1);
        const hasSpaceAfterAt = textAfterAt.includes(' ');

        if (!hasSpaceAfterAt) {
          // Filter team members based on what user is typing
          const filteredMembers = teamMembers.filter((member) =>
            member.fullName.toLowerCase().includes(textAfterAt.toLowerCase()),
          );

          if (filteredMembers.length > 0) {
            setMentionFilter(textAfterAt);
            setShowMentionDropdown(true);
            setSelectedMentionIndex(0);
            setDropdownPosition(DROPDOWN_POSITION);
            return;
          }
        }
      }

      setShowMentionDropdown(false);
      setSelectedMentionIndex(0);
    },
    [teamMembers, convertDisplayedContentToStored, form],
  );

  const handleSubmitComment = useCallback(async (data: z.infer<typeof FormSchema>) => {
    setIsSubmittingComment(true);

    await createCommentMutation.mutateAsync(
      {
        entityId,
        entitySlug,
        entityTitle,
        status,
        content: data.content,
        entityType,
      },
      {
        onSuccess: () => {
          toast.success('Comment posted', {
            description: 'Your comment has been successfully posted.',
          });

          form.reset(undefined, { keepDefaultValues: true });
        },
        onError: (error) => {
          console.error('Failed to post comment:', error);
          toast.error('Failed to post comment', {
            description: error.message || 'Failed to post comment. Please try again.',
          });
        },
        onSettled: () => {
          setIsSubmittingComment(false);
        },
      },
    );
  }, []);

  const handleSelectMention = useCallback(
    (userId: string) => {
      if (!textareaRef.current) return;

      const displayedText = getTextContent();
      const cursorPosition = getCursorPosition();
      const beforeCursor = displayedText.substring(0, cursorPosition);
      const lastAtSymbol = beforeCursor.lastIndexOf('@');

      if (lastAtSymbol !== -1) {
        const beforeMention = displayedText.substring(0, lastAtSymbol);
        const afterCursor = displayedText.substring(cursorPosition);

        // Create stored format (with userId)
        const newStoredComment = `${convertDisplayedContentToStored(beforeMention)}@${userId} ${convertDisplayedContentToStored(afterCursor)}`;

        // Find the user's full name for display and cursor positioning
        const selectedUser = teamMembers.find((member) => member.id === userId);
        const displayName = selectedUser?.fullName || userId;

        // Create displayed format (with full names)
        const newDisplayedComment = `${beforeMention}@${displayName} ${afterCursor}`;

        form.setValue('content', newStoredComment);
        setShowMentionDropdown(false);

        // Update textarea with displayed format
        if (textareaRef.current) {
          textareaRef.current.value = newDisplayedComment;
        }

        setTimeout(() => {
          // Calculate cursor position based on the displayed name length, not userId length
          const newCursorPos = lastAtSymbol + displayName.length + 2; // +1 for @, +1 for space
          setCursorPosition(newCursorPos);
        }, 0);
      }
    },
    [
      getTextContent,
      getCursorPosition,
      formatMentions,
      setCursorPosition,
      teamMembers,
      convertDisplayedContentToStored,
      form,
    ],
  );

  const handleKeyDown = useCallback(
    async (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      // Handle Cmd+Enter (Mac) or Ctrl+Enter (Windows/Linux) to submit comment
      if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
        await form.trigger();
        if (form.formState.isValid && !isSubmittingComment) {
          form.handleSubmit((data) => handleSubmitComment(data))(e);
        }
        return;
      }

      if (showMentionDropdown) {
        // Get filtered members for navigation
        const filteredMembers = teamMembers.filter((member) =>
          member.fullName.toLowerCase().includes(mentionFilter.toLowerCase()),
        );

        switch (e.key) {
          case 'Escape':
            setShowMentionDropdown(false);
            setSelectedMentionIndex(0);
            e.preventDefault();
            break;
          case 'ArrowDown':
            setSelectedMentionIndex((prev) => (prev < filteredMembers.length - 1 ? prev + 1 : 0));
            e.preventDefault();
            break;
          case 'ArrowUp':
            setSelectedMentionIndex((prev) => (prev > 0 ? prev - 1 : filteredMembers.length - 1));
            e.preventDefault();
            break;
          case 'Enter':
            if (
              filteredMembers.length > 0 &&
              selectedMentionIndex >= 0 &&
              selectedMentionIndex < filteredMembers.length
            ) {
              const selectedMember = filteredMembers[selectedMentionIndex];
              handleSelectMention(selectedMember.id);
              e.preventDefault();
            }
            break;
        }
      }
    },
    [
      showMentionDropdown,
      selectedMentionIndex,
      handleSelectMention,
      teamMembers,
      mentionFilter,
      form,
      handleSubmitComment,
      isSubmittingComment,
    ],
  );

  const handleDeleteComment = useCallback((commentId: string) => {
    setCommentToDelete(commentId);
    setShowDeleteConfirm(true);
  }, []);

  const confirmDeleteComment = useCallback(async () => {
    if (!commentToDelete) return;

    setIsDeletingComment(true);
    await deleteCommentMutation.mutateAsync(
      { id: commentToDelete },
      {
        onSuccess: () => {
          toast.success('Comment deleted', {
            description: 'The comment has been successfully deleted.',
          });
        },
        onError: (error) => {
          console.error('Failed to delete comment:', error);
          toast.error('Failed to delete comment', {
            description: error.message || 'Failed to delete comment. Please try again.',
          });
        },
        onSettled: () => setIsDeletingComment(false),
      },
    );
  }, [commentToDelete, deleteCommentMutation]);

  const cancelDeleteComment = useCallback(() => {
    setShowDeleteConfirm(false);
    setCommentToDelete(null);
  }, []);

  // Effects
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        showMentionDropdown &&
        dropdownRef.current &&
        textareaRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !textareaRef.current.contains(event.target as Node)
      ) {
        setShowMentionDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showMentionDropdown]);

  return (
    <Card className="shadow-xs bg-white">
      <CardHeader>
        <div className="flex items-center">
          <MessageSquare className="h-5 w-5 mr-2" />
          <CardTitle className="text-base">Comments</CardTitle>
        </div>
      </CardHeader>
      <CardContent className="px-4 pb-4">
        {fetchedComments.length > 0 ? (
          <div className="space-y-4" ref={commentContainerRef}>
            {fetchedComments.map((comment: Comment) => (
              <div key={comment.id} className="bg-gray-50 rounded-md p-4">
                <div className="flex items-start space-x-3">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="flex h-full w-full items-center justify-center rounded-full bg-muted text-xs">
                      {users.find((u) => u.id === comment.userId)?.firstName?.charAt(0) || '?'}
                      {users.find((u) => u.id === comment.userId)?.lastName?.charAt(0) || ''}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-sm">
                          {users.find((u) => u.id === comment.userId)?.fullName || ''}
                        </span>
                        <span className="text-xs text-gray-500">{formatDate(comment.createdAt)}</span>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 hover:bg-accent hover:text-accent-foreground"
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-36">
                          <DropdownMenuItem
                            className="text-destructive focus:text-destructive flex items-center cursor-pointer"
                            onClick={() => handleDeleteComment(comment.id)}
                          >
                            <Trash2 className="h-3.5 w-3.5 mr-2" />
                            <span>Delete</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <p className="text-sm  text-gray-700">{formatDisplayedMentions(comment.content)}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            {/* Loading state */}
            {isLoadingComments ? (
              <div className="text-center py-8 text-gray-500">Loading comments...</div>
            ) : (
              <>
                <MessageSquare className="h-8 w-8 mb-2 mx-auto opacity-50" />
                <p>No comments yet</p>
                {canCreateComment && <p className="text-xs mt-1">Be the first to add a comment</p>}
              </>
            )}
          </div>
        )}

        {/* Comment Form */}
        {canCreateComment && (
          <div className="mt-4 py-4 border-t sticky bottom-0 bg-white">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmitComment)}>
                <div className="flex gap-2">
                  <Avatar className="h-7 w-7 sm:h-8 sm:w-8 shrink-0 self-start mt-2">
                    <AvatarFallback className="text-xs">
                      {user?.firstName?.charAt(0) || '?'}
                      {user?.lastName?.charAt(0) || ''}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 relative">
                    <FormField
                      control={form.control}
                      name="content"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Textarea
                              placeholder="Add a comment or tag a teammate using @..."
                              className="min-h-[60px] focus:min-h-[100px] transition-all"
                              {...field}
                              ref={(e) => {
                                textareaRef.current = e;
                                field.ref(e);
                              }}
                              value={(() => {
                                // Convert stored format (userIds) to displayed format (full names) for display
                                let displayedValue = field.value || '';
                                teamMembers.forEach((member) => {
                                  const userIdMentionRegex = new RegExp(`@${member.id}\\b`, 'gi');
                                  displayedValue = displayedValue.replace(userIdMentionRegex, `@${member.fullName}`);
                                });
                                return displayedValue;
                              })()}
                              onChange={handleCommentChange}
                              onKeyDown={(e) => handleKeyDown(e)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* @mention dropdown */}
                    {showMentionDropdown && (
                      <div
                        className="absolute z-50 w-72 max-h-60 overflow-auto bg-white border border-gray-200 rounded-md shadow-md"
                        style={{
                          top: `${dropdownPosition.top}px`,
                          left: `${dropdownPosition.left}px`,
                        }}
                        ref={dropdownRef}
                      >
                        {(() => {
                          const filteredMembers = teamMembers.filter((member) =>
                            member.fullName.toLowerCase().includes(mentionFilter.toLowerCase()),
                          );

                          return filteredMembers.length > 0 ? (
                            <div className="py-1">
                              {filteredMembers.map((member: TeamMemberMention, index: number) => (
                                <div
                                  key={member.id}
                                  className={`flex items-center p-3 hover:bg-gray-50 cursor-pointer transition-colors duration-150 ${
                                    selectedMentionIndex === index ? 'bg-gray-100' : ''
                                  }`}
                                  onClick={() => handleSelectMention(member.id)}
                                >
                                  <Avatar className="h-6 w-6 mr-3">
                                    <AvatarFallback className="text-xs">
                                      {member.fullName.charAt(0).toUpperCase()}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div className="font-medium text-sm text-gray-900">{member?.fullName || ''}</div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="p-4 text-gray-500 text-center text-sm">
                              No matching team members
                              {mentionFilter && ` for "${mentionFilter}"`}
                            </div>
                          );
                        })()}
                      </div>
                    )}

                    <div className="flex justify-between items-center mt-2">
                      <div className="text-xs text-gray-500 space-y-1">
                        <div>Use @username to mention</div>
                        <div className="opacity-75">
                          Press{' '}
                          <kbd className="px-1 py-0.5 text-xs bg-gray-100 border rounded">
                            {navigator.userAgent.toLowerCase().includes('mac') ? 'Cmd' : 'Ctrl'}
                          </kbd>
                          {' + '}
                          <kbd className="px-1 py-0.5 text-xs bg-gray-100 border rounded">Enter</kbd> to send
                        </div>
                      </div>
                      <Button
                        type="submit"
                        disabled={!form.watch('content')?.trim() || isSubmittingComment || !form.formState.isValid}
                        aria-label="Post comment"
                      >
                        <span className="hidden sm:inline mr-1">{isSubmittingComment ? 'Posting...' : 'Post'}</span>
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </form>
            </Form>
          </div>
        )}
      </CardContent>

      {/* Delete Comment Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Comment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this comment? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDeleteComment} disabled={isDeletingComment}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteComment}
              disabled={isDeletingComment}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeletingComment ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
};
