import { StepFooter } from '@/components/sop/step-footer';
import { StepProgress } from '@/components/sop/step-progress';
import { Scoped, steps, useStepper } from '@/components/sop/stepper';
import { SopSteps } from '@/components/sop/steps';
import { Form } from '@/components/ui/form';
import { usePermissions } from '@/hooks/use-permissions';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { approvalStatusEnum } from '@shared/schema';
import { CreateSopFormSchema, CreateSopType } from '@shared/types/sop.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { ClipboardList } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Redirect, useLocation } from 'wouter';

const FormSchema = CreateSopFormSchema;
type FormType = CreateSopType;

export default function NewSop() {
  const [_, navigate] = useLocation();

  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const utils = trpc.useUtils();
  const { current } = useStepper();
  const currentStepIndex = steps.findIndex((step) => step.id === current.id);

  const mode = 'create';

  const form = useForm<FormType>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      sop: {
        title: undefined,
        ownerId: undefined,
        approverId: undefined,
        reviewDate: undefined,
        locationId: undefined,
        notes: '',
        isPublic: false,
        status: approvalStatusEnum.enumValues[0],
      },
      sections: [
        {
          sectionType: 'step',
          serial: 1,
          label: '',
          value: '',
          hazardIds: [],
          controlMeasureIds: [],
          hazardsToCreate: [],
          controlMeasuresToCreate: [],
          severity: 1,
          likelihood: 1,
        },
      ],
    },
    mode: 'onSubmit',
  });

  const { mutateAsync: createSop } = trpc.sop.create.useMutation({
    onSuccess: () => {
      utils.sop.invalidate?.(); // Invalidate SOP queries if they exist
    },
    onError: (error) => {
      console.error('Error creating SOP', error);
      toast.error('Error creating SOP', {
        description: 'There was a problem creating your SOP. Please try again.',
      });
    },
  });

  const onSubmit = async (values: FormType) => {
    setIsSubmitting(true);
    try {
      const createdSop = await createSop({
        sections: values.sections,
        sop: values.sop,
      });
      // Track SOP creation
      console.log('SOP created:', {
        sop_id: createdSop?.id,
        sections_count: values.sections.length,
        location: values.sop.locationId || '',
      });
      toast.success('SOP created successfully', {
        description: 'Your Standard Operating Procedure has been created and is ready for review.',
      });
      form.reset();
      if (createdSop?.instanceId) {
        // Navigate to SOP details if we have instanceId
        navigate(ROUTES.BUILD_SOP_DETAILS_PATH(createdSop?.instanceId));
      } else {
        navigate(ROUTES.SOP_LIST);
      }
    } catch (error) {
      console.error('Error submitting SOP', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.log('SOP form validation failed:', {
        validation_errors: [errorMessage],
        error_message: errorMessage,
      });
      toast.error('Error creating SOP', {
        description: 'There was a problem creating your SOP. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const { hasPermission: checkPermission } = usePermissions();

  if (!checkPermission(MODULES.SOP, ALLOWED_ACTIONS.CREATE)) {
    return <Redirect to={ROUTES.SOP_LIST} />;
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Scoped>
          <div className="max-w-6xl mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6">
            <div className="space-y-8">
              <div className="flex items-center gap-5">
                <div className="flex flex-col">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <ClipboardList className="w-6 h-6 text-blue-600" />
                    </div>
                    <h1 className="text-2xl font-bold text-gray-900">SOP Generation Wizard</h1>
                  </div>
                  <p className="text-gray-600 text-base">
                    Step {currentStepIndex + 1} of {steps.length}
                  </p>
                </div>
              </div>

              <StepProgress />
              <SopSteps mode={mode} />
              <StepFooter isSubmitting={isSubmitting} mode={mode} />
            </div>
          </div>
        </Scoped>
      </form>
    </Form>
  );
}
