import { AsyncAssetMultiSelect } from '@/components/composite/async-asset-multi-select';
import { AsyncLocationSelect } from '@/components/composite/async-location-select';
import { AsyncUserSelect } from '@/components/composite/async-user-select';
import { Back } from '@/components/composite/back';
import { JhaStatusBadge } from '@/components/composite/jha-status-badge';
import { EditJhaError } from '@/components/jha/edit/edit-jha-error';
import { EditJhaLoading } from '@/components/jha/edit/edit-jha-loading';
import { JhaReasonForRevision } from '@/components/jha/edit/jha-reason-for-revision';
import { JhaSteps } from '@/components/jha/steps';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { TextareaWithWritingAssistant } from '@/components/ui/textarea-with-writing-assistant';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { usePermissions } from '@/hooks/use-permissions';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { approvalStatusEnum } from '@shared/schema';
import { UpdateJhaFormSchema, UpdateJhaType } from '@shared/types/jha.types';
import { ALLOWED_ACTIONS, MODULES } from '@shared/user-permissions';
import { addYears } from 'date-fns';
import { InfoIcon } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { Redirect, useLocation } from 'wouter';

const FormSchema = UpdateJhaFormSchema;

type FormType = UpdateJhaType;

export default function EditJha({ params }: { params: { id: string } }) {
  const jhaInstanceId = params.id;
  const [_, navigate] = useLocation();
  const { hasPermission: checkPermission } = usePermissions();

  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [showReasonForRevisionModal, setShowReasonForRevisionModal] = useState<boolean>(false);
  const [reasonForRevision, setReasonForRevision] = useState<string>('');

  const utils = trpc.useUtils();

  const {
    data: jha,
    isLoading,
    error: jhaError,
  } = trpc.jha.getByInstanceIdForEdit.useQuery({
    id: jhaInstanceId,
  });

  const isInitiatingRevision = useMemo(() => jha?.status === approvalStatusEnum.enumValues[2], [jha?.status]);

  const form = useForm<FormType>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      jha: {
        title: undefined,
        ownerId: undefined,
        approverId: undefined,
        reviewDate: undefined,
        locationId: undefined,
        assetIds: undefined,
        notes: '',
        isPublic: false,
      },
      steps: [
        {
          serial: 1,
          title: '',
          hazardIds: [],
          controlMeasureIds: [],
          severity: 1,
          likelihood: 1,
          description: '',
        },
      ],
    },
    mode: 'onSubmit',
  });

  useEffect(() => {
    if (jha) {
      form.reset({
        jha: {
          id: jha.id,
          title: jha.title,
          ownerId: jha.ownerId,
          approverId: jha.approverId,
          reviewDate: jha.reviewDate,
          locationId: jha.locationId,
          assetIds: jha.assetIds,
          notes: jha.notes,
          isPublic: jha.isPublic,
        },
        steps: jha.steps.map((step) => ({
          id: step.id,
          serial: step.serial,
          title: step.title,
          hazardIds: step.hazardIds,
          controlMeasureIds: step.controlMeasureIds,
          severity: step.severity,
          likelihood: step.likelihood,
          description: step.description,
        })),
      });
    }
  }, [jha]);

  const { mutate: createJha } = trpc.jha.create.useMutation({
    onSuccess: () => {
      utils.jha.invalidate?.();
      toast.success('JHA updated successfully', {
        description: 'Your Job Hazard Analysis has been updated and is ready for review.',
      });
    },
    onError: (error) => {
      console.error('Error updating JHA', error);
      toast.error('Error updating JHA', {
        description: 'There was a problem updating your JHA. Please try again.',
      });
    },
  });

  const { mutateAsync: updateJha } = trpc.jha.update.useMutation({
    onSuccess: () => {
      utils.jha.invalidate?.();
      toast.success('JHA updated successfully', {
        description: 'Your Job Hazard Analysis has been updated and is ready for review.',
      });
    },
    onError: (error) => {
      console.error('Error updating JHA', error);
      toast.error('Error updating JHA', {
        description: 'There was a problem updating your JHA. Please try again.',
      });
    },
  });

  useEffect(() => {
    if (form.watch('jha.locationId')) {
      form.setValue('jha.assetIds', []);
      utils.asset.search.invalidate();
    }
  }, [form.watch('jha.locationId'), form, utils.asset.search]);

  const onSubmit = async (values: FormType) => {
    if (isInitiatingRevision && !reasonForRevision) {
      toast.error('Error updating JHA', {
        description: 'Please add a reason for revision.',
      });
      setShowReasonForRevisionModal(true);
      return;
    }

    if (!isInitiatingRevision && !jha?.id) {
      toast.error('Error updating JHA', {
        description: 'There was a problem updating your JHA. Please try again.',
      });
      return;
    }

    const mutation = isInitiatingRevision ? createJha : updateJha;

    try {
      mutation({
        jha: {
          ...values.jha,
          instanceId: jha?.instanceId,
          ...(isInitiatingRevision && reasonForRevision && { reasonForRevision }),
        },
        steps: values.steps,
      });

      // Reset form and navigate to JHA list
      form.reset();
      navigate(ROUTES.BUILD_JHA_DETAILS_PATH(jha?.instanceId!));
    } catch (error) {
      console.error('Error submitting JHA', error);
      // Track form validation failed
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.log('JHA form validation failed:', {
        validation_errors: [errorMessage],
        error_message: errorMessage,
      });
      toast.error('Error updating JHA', {
        description: 'There was a problem updating your JHA. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!checkPermission(MODULES.EHS_JHA, ALLOWED_ACTIONS.CREATE)) {
    return <Redirect to={ROUTES.JHA_LIST} />;
  }

  if (isLoading) {
    return <EditJhaLoading />;
  }

  if (jhaError || !jha || jha.status === approvalStatusEnum.enumValues[1]) {
    return <EditJhaError />;
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6">
      {/* Header section with back button and title */}
      <div className="flex items-center justify-between">
        <Back />
        <h1 className="text-2xl font-bold text-primary-600">Edit #{jha.slug}</h1>
      </div>
      <div>
        <Separator className="my-4" />

        <JhaReasonForRevision
          isOpen={showReasonForRevisionModal}
          onClose={() => setShowReasonForRevisionModal(false)}
          onSave={(reason) => {
            setReasonForRevision(reason);
            setShowReasonForRevisionModal(false);
          }}
          isLoading={isSubmitting}
        />

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Basic Information */}
            <div className="space-y-6">
              <div className="border-b pb-4 flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">Basic Information</h2>
                  <p className="text-sm text-gray-600 mt-1">
                    Provide the basic details about this Job Hazard Analysis.
                  </p>
                </div>
                <div className="flex flex-col items-end gap-2">
                  <JhaStatusBadge status={jha.status} />
                  <Badge variant="outline">Version {jha.version}</Badge>
                </div>
              </div>

              {/* JHA Title - Full width */}
              <FormField
                control={form.control}
                name="jha.title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      JHA Title <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Enter the job or task title" {...field} value={field.value || ''} />
                    </FormControl>
                    <FormDescription>
                      A clear, descriptive title for the job or task being analyzed (e.g., "Operating Forklift in
                      Warehouse")
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Owner and Approver - Responsive grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="jha.ownerId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Owner <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <AsyncUserSelect
                          placeholder="Select JHA owner"
                          value={field.value}
                          onChange={field.onChange}
                          excludeViewOnly={true}
                        />
                      </FormControl>
                      <FormDescription>The person responsible for creating and maintaining this JHA</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="jha.approverId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Approver <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <AsyncUserSelect
                          placeholder="Select JHA approver"
                          value={field.value}
                          onChange={field.onChange}
                          excludeViewOnly={true}
                        />
                      </FormControl>
                      <FormDescription>The supervisor or manager who will approve this JHA</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Location, Assets, and Review Date - Responsive grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 items-start">
                <FormField
                  control={form.control}
                  name="jha.locationId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location</FormLabel>
                      <FormControl>
                        <AsyncLocationSelect
                          value={field.value}
                          onChange={field.onChange}
                          placeholder="Where is this job performed?"
                        />
                      </FormControl>
                      <FormDescription>
                        Specific area, building, or department where the job is performed
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="jha.assetIds"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Equipment/Assets</FormLabel>
                      <FormControl>
                        <AsyncAssetMultiSelect
                          {...field}
                          value={field.value || []}
                          onChange={field.onChange}
                          placeholder="Select equipment used"
                          locationId={form.watch('jha.locationId') ?? undefined}
                        />
                      </FormControl>
                      <FormDescription>Equipment, tools, or assets involved in this job</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="jha.reviewDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Next Review Date</FormLabel>
                      <FormControl>
                        <DateTimePicker
                          selected={field.value || undefined}
                          onSelect={field.onChange}
                          disabled={{ before: new Date() }}
                          onlyDate
                          startMonth={new Date()}
                          endMonth={addYears(new Date(), 5)}
                        />
                      </FormControl>
                      <FormDescription>When should this JHA be reviewed next?</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Task Steps & Risk Assessment */}
            <div>
              <div className="pb-4">
                <h2 className="text-xl font-semibold text-gray-900">Task Steps & Risk Assessment</h2>
                <p className="text-sm text-gray-600 mt-1">
                  List each step of the job in the order it's performed. For each step, identify the hazard, estimate
                  its severity and likelihood, then define the control that reduces risk. Drag steps to re-order. Risk
                  Score updates automatically.
                </p>
              </div>

              <JhaSteps />
            </div>

            {/* Additional Notes */}
            <div className="space-y-6">
              <div className="border-b pb-4">
                <h2 className="text-xl font-semibold text-gray-900">Additional Notes</h2>
                <p className="text-sm text-gray-600 mt-1">
                  Add any additional context, requirements, or information about this job hazard analysis.
                </p>
              </div>

              <FormField
                control={form.control}
                name="jha.notes"
                render={({ field }) => (
                  <TextareaWithWritingAssistant
                    field={field}
                    label="Additional Notes"
                    placeholder="Any additional information about this JHA"
                    description="Add any additional context, requirements, or information about this job hazard analysis."
                    context="Job Hazard Analyses document - Any additional information about this JHA"
                    minHeight="80px"
                  />
                )}
              />
            </div>

            <div className="flex justify-between items-center border-t pt-8">
              <div>
                <FormField
                  control={form.control}
                  name="jha.isPublic"
                  render={({ field }) => (
                    <FormItem className="flex items-center gap-2">
                      <FormControl>
                        <div className="flex items-center gap-2">
                          <Checkbox name={field.name} checked={field.value ?? false} onCheckedChange={field.onChange} />
                          <Label htmlFor={field.name}>
                            Make Public
                            <Tooltip>
                              <TooltipTrigger>
                                <InfoIcon className="w-4 h-4" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <strong>Public Visibility</strong> <br /> Checking this box will make this JHA
                                accessible in a read-only format to <br /> all active users within your organization,
                                including those with 'View-Only' access. <br /> This allows broader access to general
                                safety procedures.
                                <br />
                                <br />
                                <strong>Note:</strong> This will not affect the visibility of the JHA to the owner or
                                approver.
                              </TooltipContent>
                            </Tooltip>
                          </Label>
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex justify-end space-x-4">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => {
                    form.reset();
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                  }}
                >
                  Reset
                </Button>

                {!isInitiatingRevision && (
                  <Button type="submit" disabled={isSubmitting || !form.formState.isDirty}>
                    {isSubmitting ? 'Updating JHA...' : 'Update JHA'}
                  </Button>
                )}

                {isInitiatingRevision && !reasonForRevision && (
                  <Button
                    type="button"
                    disabled={isSubmitting || !form.formState.isDirty}
                    onClick={() => {
                      setShowReasonForRevisionModal(true);
                    }}
                  >
                    Add Reason for Revision
                  </Button>
                )}

                {isInitiatingRevision && reasonForRevision && (
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? 'Updating JHA...' : 'Update JHA with Reason for Revision'}
                  </Button>
                )}
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
