import { AsyncEventsSelect } from '@/components/composite/async-events-select';
import { AsyncOshaLocationSelect } from '@/components/composite/async-osha-location-select';
import { Back } from '@/components/composite/back';
import { EditOshaReportLoading } from '@/components/osha-reports/edit/edit-osha-report-loading';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardAction, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { DateTimePicker } from '@/components/ui/date-time-picker';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { TextareaWithWritingAssistant } from '@/components/ui/textarea-with-writing-assistant';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { trpc } from '@/providers/trpc';
import { zodResolver } from '@hookform/resolvers/zod';
import { ROUTES } from '@shared/ROUTE_PATHS';
import { oshaTypeEnum, shiftsEnum, typeOfMedicalCareEnum } from '@shared/schema';
import {
  EditOshaReportForm,
  EditOshaReportFormSchema,
  OSHA_TYPE_MAP,
  SHIFTS_MAP,
  TYPE_OF_MEDICAL_CARE_MAP,
} from '@shared/types/osha.types';
import { HelpCircle, Info, Loader2, Save } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { useLocation, useParams } from 'wouter';

const FormSchema = EditOshaReportFormSchema;

type FormType = EditOshaReportForm;

export default function EditOshaReport({ params }: { params: { id: string } }) {
  const { eventId } = useParams();
  const utils = trpc.useUtils();

  // Fetch the existing CAPA data
  const oshaReportId = params.id;

  const [_, navigate] = useLocation();

  const [isSubmitting, setIsSubmitting] = useState(false);

  const { mutateAsync: updateOshaReport } = trpc.oshaReport.update.useMutation({
    onSuccess: () => {
      utils.oshaReport.getById.invalidate({ id: oshaReportId });
      toast.success('OSHA Form 301 Updated', {
        description: 'The OSHA log has been updated successfully.',
      });
    },
    onError: () => {
      toast.error('Error updating OSHA form', {
        description: 'There was a problem updating the OSHA form. Please try again.',
      });
    },
  });

  const {
    data: oshaReport,
    isLoading: isLoadingOshaReport,
    error,
  } = trpc.oshaReport.getByIdForEdit.useQuery({
    id: oshaReportId,
  });

  useEffect(() => {
    if (error?.data?.code === 'FORBIDDEN') {
      navigate(ROUTES.NOT_FOUND);
    }
  }, [error]);

  const form = useForm<FormType>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      // Linked safety event
      eventId: eventId || undefined,

      // Section 1 - Employee Info
      privacyCase: false,
      employeeName: '',
      employeeDepartment: '',
      employeeJobTitle: '',
      employeeWorkLocation: '',
      employeeDateOfHire: undefined,
      employeeShift: shiftsEnum.enumValues[0],
      employeeSupervisor: '',
      oshaLocationId: undefined,

      // Section 2 - Incident Details

      // Section 3 - Nature of Injury
      bodyPartInjured: '',
      typeOfInjury: '',
      treatmentLocation: '',
      typeOfMedicalCare: typeOfMedicalCareEnum.enumValues[0],
      prescribedMedication: false,

      // Section 4 - OSHA Questions
      wasHospitalized: false,
      wasDeceased: false,
      daysAwayFromWork: undefined,
      daysRestrictedFromWork: undefined,

      // Section 5 - People & Witnesses
      witnesses: '',
      reportedByName: '',

      // Section 6 - Corrective Actions
      rootCauseAnalysis: '',
      correctiveActions: '',

      // Section 7 - OSHA Reporting
      type: oshaTypeEnum.enumValues[3],
      reasonForReport: '',
      reasonForPrivacyCase: '',
    },
    mode: 'onSubmit',
  });

  // Clear hospitalization and days fields when deceased is selected
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'wasDeceased' && value.wasDeceased) {
        form.setValue('wasHospitalized', false);
        form.setValue('daysAwayFromWork', undefined);
        form.setValue('daysRestrictedFromWork', undefined);
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  useEffect(() => {
    if (oshaReport) {
      form.reset({
        ...oshaReport,
        eventId: oshaReport.eventId,
        employeeName: oshaReport.employeeName ?? '',
        employeeDepartment: oshaReport.employeeDepartment ?? '',
        employeeJobTitle: oshaReport.employeeJobTitle ?? '',
        employeeWorkLocation: oshaReport.employeeWorkLocation ?? '',
        daysAwayFromWork: oshaReport.daysAwayFromWork ?? undefined,
        daysRestrictedFromWork: oshaReport.daysRestrictedFromWork ?? undefined,
        bodyPartInjured: oshaReport.bodyPartInjured,
        typeOfInjury: oshaReport.typeOfInjury,
        typeOfMedicalCare: oshaReport.typeOfMedicalCare,
        oshaLocationId: oshaReport.oshaLocationId,
      });
    }
  }, [oshaReport]);

  function handleBackToIncident() {
    if (eventId) {
      navigate(ROUTES.BUILD_EVENT_DETAILS_PATH(eventId));
    } else {
      navigate(ROUTES.BUILD_OSHA_REPORT_DETAILS_PATH(oshaReportId));
    }
  }

  async function onSubmit(values: FormType) {
    try {
      const toUpdate: Partial<FormType> = {};

      for (const field in form.formState.dirtyFields) {
        if (form.formState.dirtyFields[field as keyof typeof form.formState.dirtyFields]) {
          (toUpdate as Record<string, unknown>)[field] = values[field as keyof FormType];
        }
      }

      // If wasHospitalized or wasDeceased are being updated to true,
      // ensure the dependent fields are included in the update
      if (values.wasHospitalized || values.wasDeceased) {
        if ('wasHospitalized' in toUpdate || 'wasDeceased' in toUpdate) {
          (toUpdate as Record<string, unknown>)['daysAwayFromWork'] = values.daysAwayFromWork;
          (toUpdate as Record<string, unknown>)['daysRestrictedFromWork'] = values.daysRestrictedFromWork;
        }
      }

      await updateOshaReport({
        oshaLocationId: values.oshaLocationId,
        ...toUpdate,
        id: oshaReportId,
      });

      form.reset();
      navigate(ROUTES.BUILD_OSHA_REPORT_DETAILS_PATH(oshaReportId));
    } catch (error) {
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  }

  if (isLoadingOshaReport) {
    return <EditOshaReportLoading />;
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 md:px-8 lg:px-10 py-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <Back onClick={handleBackToIncident} />
          <h1 className="text-2xl font-bold text-neutral-black">Edit #{oshaReport?.slug}</h1>
        </div>

        {/* Header with title and action buttons */}

        {/* Status information area */}
        <div className="flex flex-wrap items-center gap-2">
          {eventId && <span className="text-sm text-muted-foreground">Linked to Safety Event #{eventId}</span>}
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  1
                </span>
                <h2 className=" font-semibold">
                  Linked Safety Event <span className="text-red-500">*</span>
                </h2>
              </CardTitle>
              <CardDescription>This is a mandatory field.</CardDescription>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="eventId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Select Safety Event</FormLabel>
                    <AsyncEventsSelect
                      {...field}
                      onChange={field.onChange}
                      value={field.value}
                      mustIncludeObjectIds={eventId ? [eventId] : undefined}
                      placeholder="Search and select a safety event..."
                    />
                    <FormDescription>
                      Associate this OSHA record with an existing safety event for better tracking.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {form.watch('eventId') && (
                <CardAction>
                  <a
                    href={ROUTES.BUILD_EVENT_DETAILS_PATH(form.watch('eventId')!)}
                    className="text-primary hover:underline cursor-pointer text-sm"
                    target="_blank"
                  >
                    Go to Safety Event
                  </a>
                </CardAction>
              )}
            </CardContent>
          </Card>

          {/* Section 2: Employee Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  2
                </span>
                Employee Information
              </CardTitle>
              <CardDescription>Identify the employee involved in the incident</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Privacy Case Toggle */}
              <FormField
                control={form.control}
                name="privacyCase"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border border-blue-300 bg-blue-50 p-4 shadow-sm">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="mr-2 bg-blue-100 text-blue-800 border-blue-300 px-2 py-1">
                        🛡️ PRIVACY
                      </Badge>
                      <span className="text-md font-semibold text-blue-800">Mark as Privacy Case</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-blue-500" />
                          </TooltipTrigger>
                          <TooltipContent className="w-[300px] p-4 shadow-lg rounded-md border">
                            <div className="space-y-2">
                              <h4 className="font-semibold">OSHA Privacy Case Criteria</h4>
                              <p className="text-sm">Mark this case as private if it involves any of the following:</p>
                              <ul className="text-sm list-disc pl-4 space-y-1">
                                <li>Injury to intimate body part or reproductive system</li>
                                <li>Sexual assault</li>
                                <li>Mental illness</li>
                                <li>HIV infection, hepatitis, or tuberculosis</li>
                                <li>Needle stick injury from contaminated object</li>
                                <li>Employee requests privacy</li>
                              </ul>
                              <p className="text-sm pt-1">
                                When enabled, the employee's name will be replaced with "Privacy Case" in OSHA records.
                              </p>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="data-[state=checked]:bg-blue-600"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="employeeName"
                render={({ field }) => {
                  // Check if privacy case is enabled
                  const isPrivacyCase = form.watch('privacyCase');

                  return (
                    <FormItem>
                      <FormLabel>
                        Employee Name
                        <span className="text-red-500 ml-1">*</span>
                      </FormLabel>
                      <FormControl>
                        {isPrivacyCase ? (
                          <div className="p-3 bg-blue-50 border border-blue-200 rounded-md text-blue-800 flex items-center">
                            <span className="mr-2">🛡️</span>
                            Privacy Case — name will not be displayed in OSHA logs
                          </div>
                        ) : (
                          <Input {...field} placeholder="Employee name" value={field.value ?? ''} />
                        )}
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />

              {form.watch('privacyCase') && (
                <FormField
                  control={form.control}
                  name="reasonForPrivacyCase"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormLabel>
                          Reason for Privacy Case
                          <span className="text-red-500 ml-1">*</span>
                        </FormLabel>
                        <FormControl>
                          <Textarea {...field} placeholder="Reason for privacy case" value={field.value ?? ''} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              )}

              <FormField
                control={form.control}
                name="employeeJobTitle"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Job Title <span className="text-red-500 ml-1">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Job position or title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="employeeWorkLocation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Work Location
                      <span className="text-red-500 ml-1">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Warehouse, Office, etc." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="employeeDepartment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Department</FormLabel>
                    <FormControl>
                      <Input placeholder="Employee department" {...field} value={field.value ?? ''} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="employeeDateOfHire"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date of Hire</FormLabel>
                      <FormControl>
                        <DateTimePicker
                          selected={field.value ?? undefined}
                          onSelect={field.onChange}
                          className="w-full"
                          onlyDate
                          placeholder="Select date of hire"
                          disabled={{
                            after: new Date(),
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="employeeShift"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Shift</FormLabel>
                      <FormControl>
                        <Select onValueChange={field.onChange} value={field.value ?? undefined}>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select shift" />
                          </SelectTrigger>
                          <SelectContent>
                            {shiftsEnum.enumValues.map((shift) => (
                              <SelectItem key={shift} value={shift}>
                                {SHIFTS_MAP[shift]}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="employeeSupervisor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Supervisor</FormLabel>
                    <FormControl>
                      <Input placeholder="Employee's supervisor name" {...field} value={field.value ?? ''} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="oshaLocationId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      OSHA Location <span className="text-red-500 ml-1">*</span>
                    </FormLabel>
                    <FormControl>
                      <AsyncOshaLocationSelect
                        onChange={field.onChange}
                        value={field.value}
                        placeholder="Select OSHA location"
                        mustIncludeObjectIds={oshaReport?.oshaLocationId ? [oshaReport.oshaLocationId] : undefined}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Section 3: Medical Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  3
                </span>
                Medical Information
              </CardTitle>
              <CardDescription>Details about the injury or illness and incident context</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="bodyPartInjured"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Body Part Affected
                      <span className="text-red-500 ml-1">*</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span>
                              <HelpCircle className="h-3.5 w-3.5 ml-1 text-muted-foreground" />
                            </span>
                          </TooltipTrigger>
                          <TooltipContent className="max-w-xs">
                            <p>Specify the part of body that was affected</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Hand, back, eye, etc." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="typeOfInjury"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Type of Injury
                      <span className="text-red-500 ml-1">*</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span>
                              <HelpCircle className="h-3.5 w-3.5 ml-1 text-muted-foreground" />
                            </span>
                          </TooltipTrigger>
                          <TooltipContent className="max-w-xs">
                            <p>Specify the type of injury sustained</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Cut, burn, fracture, etc." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="treatmentLocation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Treatment Location</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Onsite first aid, hospital, clinic, etc."
                        {...field}
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="typeOfMedicalCare"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Type of Medical Care <span className="text-red-500 ml-1">*</span>
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-1"
                      >
                        {typeOfMedicalCareEnum.enumValues.map((medicalCare) => (
                          <FormItem className="flex items-center space-x-3 space-y-0" key={medicalCare}>
                            <FormControl>
                              <RadioGroupItem value={medicalCare} />
                            </FormControl>
                            <FormLabel className="font-normal">{TYPE_OF_MEDICAL_CARE_MAP[medicalCare]}</FormLabel>
                          </FormItem>
                        ))}
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="prescribedMedication"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox checked={field.value ?? false} onCheckedChange={field.onChange} />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Prescription Medication Given</FormLabel>
                      <FormDescription>
                        Check if prescription medication was provided as part of treatment
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Section 4: OSHA Questions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  4
                </span>
                OSHA Questions
              </CardTitle>
              <CardDescription>Additional information required for OSHA Record Keeping</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="wasHospitalized"
                    render={({ field }) => {
                      const wasDeceased = form.watch('wasDeceased');
                      const isDisabled = wasDeceased;

                      return (
                        <FormItem
                          className={`flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 ${isDisabled ? 'opacity-50 bg-gray-50' : ''}`}
                        >
                          <FormControl>
                            <Checkbox
                              checked={field.value && !isDisabled}
                              disabled={isDisabled}
                              onCheckedChange={(checked) => {
                                if (!isDisabled) {
                                  field.onChange(checked);
                                  // Trigger validation for the dependent fields
                                  setTimeout(() => {
                                    form.trigger(['daysAwayFromWork', 'daysRestrictedFromWork']);
                                  }, 0);
                                }
                              }}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel className={isDisabled ? 'text-gray-500' : ''}>
                              Employee was hospitalized as an in-patient{' '}
                              {!isDisabled && <span className="text-red-500 ml-1">*</span>}
                            </FormLabel>
                            <FormDescription className={isDisabled ? 'text-gray-400' : ''}>
                              {isDisabled
                                ? 'Not applicable when incident resulted in death'
                                : 'Check if overnight admission to a hospital was required'}
                            </FormDescription>
                          </div>
                        </FormItem>
                      );
                    }}
                  />

                  <FormField
                    control={form.control}
                    name="wasDeceased"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={(checked) => {
                              field.onChange(checked);
                              // If deceased is selected, clear hospitalized field
                              if (checked) {
                                form.setValue('wasHospitalized', false);
                              }
                              // Trigger validation for the dependent fields
                              setTimeout(() => {
                                form.trigger(['daysAwayFromWork', 'daysRestrictedFromWork']);
                              }, 0);
                            }}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            Incident resulted in death <span className="text-red-500 ml-1">*</span>
                          </FormLabel>
                          <FormDescription>Check if the incident resulted in a fatality</FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid md:items-start  grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="daysAwayFromWork"
                    rules={{
                      validate: (value) => {
                        const formValues = form.getValues();
                        const wasHospitalized = formValues.wasHospitalized;
                        const wasDeceased = formValues.wasDeceased;
                        // Only require when hospitalized (not when deceased)
                        if (wasHospitalized && !wasDeceased && (value === null || value === undefined)) {
                          return 'Days away from work is required when hospitalized.';
                        }
                        return true;
                      },
                    }}
                    render={({ field }) => {
                      const wasHospitalized = form.watch('wasHospitalized');
                      const wasDeceased = form.watch('wasDeceased');
                      const isRequired = wasHospitalized && !wasDeceased;
                      const isDisabled = wasDeceased;

                      return (
                        <FormItem>
                          <FormLabel className={isDisabled ? 'text-gray-500' : ''}>
                            Days Away From Work {isRequired && <span className="text-red-500 ml-1">*</span>}
                          </FormLabel>
                          <FormDescription className={isDisabled ? 'text-gray-400' : ''}>
                            {isDisabled
                              ? 'Not applicable when incident resulted in death'
                              : 'Number of days the employee was unable to work'}
                          </FormDescription>
                          <FormControl>
                            <Input
                              {...field}
                              type="number"
                              min="0"
                              disabled={isDisabled}
                              className={isDisabled ? 'bg-gray-50 text-gray-500' : ''}
                              onChange={(e) => {
                                if (!isDisabled) {
                                  const value = +e.target.value;
                                  field.onChange(isNaN(value) ? undefined : value);
                                }
                              }}
                              placeholder="Enter number of days"
                              value={isDisabled ? '' : (field.value ?? '')}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />

                  <FormField
                    control={form.control}
                    name="daysRestrictedFromWork"
                    rules={{
                      validate: (value) => {
                        const formValues = form.getValues();
                        const wasHospitalized = formValues.wasHospitalized;
                        const wasDeceased = formValues.wasDeceased;
                        // Only require when hospitalized (not when deceased)
                        if (wasHospitalized && !wasDeceased && (value === null || value === undefined)) {
                          return 'Days restricted from work is required when hospitalized.';
                        }
                        return true;
                      },
                    }}
                    render={({ field }) => {
                      const wasHospitalized = form.watch('wasHospitalized');
                      const wasDeceased = form.watch('wasDeceased');
                      const isRequired = wasHospitalized && !wasDeceased;
                      const isDisabled = wasDeceased;

                      return (
                        <FormItem>
                          <FormLabel className={isDisabled ? 'text-gray-500' : ''}>
                            Days of Restricted Work Activity
                            {isRequired && <span className="text-red-500 ml-1">*</span>}
                          </FormLabel>
                          <FormDescription className={isDisabled ? 'text-gray-400' : ''}>
                            {isDisabled
                              ? 'Not applicable when incident resulted in death'
                              : 'Days employee was on restricted duty or job transfer'}
                          </FormDescription>
                          <FormControl>
                            <Input
                              {...field}
                              type="number"
                              min="0"
                              disabled={isDisabled}
                              className={isDisabled ? 'bg-gray-50 text-gray-500' : ''}
                              placeholder="Enter number of days"
                              onChange={(e) => {
                                if (!isDisabled) {
                                  const value = +e.target.value;
                                  field.onChange(isNaN(value) ? undefined : value);
                                }
                              }}
                              value={isDisabled ? '' : (field.value ?? '')}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      );
                    }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Section 5: Witnesses & People */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  5
                </span>
                Witnesses & People
              </CardTitle>
              <CardDescription>People involved in or who witnessed the incident</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="witnesses"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Witnesses</FormLabel>
                    <FormDescription>Names of people who witnessed the incident (comma-separated list)</FormDescription>
                    <FormControl>
                      <Textarea
                        placeholder="Enter witness names separated by commas"
                        className="min-h-[60px]"
                        {...field}
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="reportedByName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reported By</FormLabel>
                    <FormDescription>
                      Optional field. Defaults to the safety event reporter. You can override this by entering a
                      different name if needed.
                    </FormDescription>
                    <FormControl>
                      <Input
                        placeholder="Enter the name of the person who reported the incident"
                        {...field}
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Section 6: Corrective Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  6
                </span>
                Corrective Actions
              </CardTitle>
              <CardDescription>Actions taken or planned to prevent recurrence</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="rootCauseAnalysis"
                render={({ field }) => (
                  <TextareaWithWritingAssistant
                    field={field}
                    label="Root Cause Analysis"
                    placeholder="Describe the root cause(s) of the incident"
                    description="Document the analysis of what caused this incident to occur"
                    context="OSHA incident root cause analysis - The user is analyzing the underlying causes of a workplace safety incident for OSHA reporting purposes."
                    minHeight="80px"
                  />
                )}
              />

              <FormField
                control={form.control}
                name="correctiveActions"
                render={({ field }) => (
                  <TextareaWithWritingAssistant
                    field={field}
                    label="Corrective Actions"
                    placeholder="Describe actions taken or planned to prevent recurrence"
                    description="Document the specific actions taken or planned to prevent this type of incident from happening again"
                    context="OSHA incident corrective actions - The user is documenting corrective and preventive measures for a workplace safety incident to prevent recurrence."
                    minHeight="80px"
                  />
                )}
              />
            </CardContent>
          </Card>

          {/* Section 7: OSHA Reporting */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="bg-primary text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                  7
                </span>
                OSHA Reporting
              </CardTitle>
              <CardDescription>OSHA reporting requirements and classification</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Primary Recordable Outcome - Mandatory Dropdown */}
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      Primary Recordable Outcome
                      <span className="text-red-500 ml-1">*</span>
                    </FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select primary recordable outcome" />
                        </SelectTrigger>
                        <SelectContent>
                          {oshaTypeEnum.enumValues.map((type) => (
                            <SelectItem key={type} value={type}>
                              {OSHA_TYPE_MAP[type]}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormDescription>Select the primary outcome that makes this case OSHA recordable</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Reason for Reportability - Optional Text Area */}
              <FormField
                control={form.control}
                name="reasonForReport"
                render={({ field }) => (
                  <TextareaWithWritingAssistant
                    field={field}
                    label="Reason for Reportability"
                    placeholder="Optional: Provide additional explanation if the dropdown selection doesn't fully cover the situation..."
                    description="Optional field for additional context or explanation"
                    context="OSHA reportability explanation - The user is writing a workplace incident report for OSHA, describing an injury caused by improper manual lifting without assistance or equipment."
                    minHeight="80px"
                  />
                )}
              />
            </CardContent>
          </Card>

          {/* Form action buttons */}
          <div className="flex justify-end">
            {/* Right side buttons - save button only in edit mode */}
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
              Save OSHA Form
            </Button>
          </div>

          {/* OSHA Record Keeping Requirement - above submit button */}

          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mt-6">
            <div className="flex gap-3">
              <Info className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
              <div>
                <h3 className="font-medium text-amber-800">OSHA Record Keeping Requirement</h3>
                <p className="text-sm text-amber-700 mt-1">
                  This form must be retained for 5 years following the year to which it pertains. Completing this form
                  will automatically update your OSHA 300 Log and 300A Summary.
                </p>
              </div>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
}
