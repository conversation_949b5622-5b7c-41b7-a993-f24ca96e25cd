import { trpc } from '@server/trpc/trpc';
import { privateProcedure } from '../trpc';
import { MODULES } from '@shared/user-permissions';
import { ALLOWED_ACTIONS } from '@shared/user-permissions';
import {
  CreateSopFormSchema,
  UpdateSopFormSchema,
  ListSopSchema,
  GetSopByInstanceIdSchema,
  UpdateSopStatusSchema
} from '@shared/types/sop.types';
import { TRPCError } from '@trpc/server';
import { createSop, updateSop, listSops, getSopByInstanceId, toggleArchiveSop, getSopVersions, updateSopStatus } from '@server/services/sop.service';

import { searchAssetsPublic } from '@server/services/asset.service';
import { searchLocationsPublic } from '@server/services/location.service';
import { getUsersPublic } from '@server/services/user.service';
import { Asset, Location } from '@shared/types/assets.types';
import { UuidSchema } from '@shared/types/schema.types';
import { UserPublic } from '@shared/types/users.types';

export const sopRouter = trpc.router({
  create: privateProcedure
    .hasPermission(MODULES.SOP, ALLOWED_ACTIONS.CREATE)
    .input(CreateSopFormSchema)
    .mutation(async ({ input, ctx }) => {
      const sop = await createSop(input, ctx.user);
      if (!sop) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to create SOP' });
      }
      return sop;
    }),
  update: privateProcedure
    .hasPermission(MODULES.SOP, ALLOWED_ACTIONS.EDIT)
    .input(UpdateSopFormSchema)
    .mutation(async ({ input, ctx }) => {
      const updatedSop = await updateSop(input, ctx.user);
      if (!updatedSop) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to update SOP' });
      }
      return updatedSop;
    }),
  getByInstanceId: privateProcedure
    .hasPermission(MODULES.SOP, ALLOWED_ACTIONS.VIEW)
    .input(GetSopByInstanceIdSchema)
    .query(async ({ input, ctx }) => {
      const sop = await getSopByInstanceId(input, ctx.user, ctx.needPartialCheck);

      if (!sop) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'SOP not found' });
      }

      // Fetch related data
      const [owner, approver, location, assets] = await Promise.all([
        getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: [sop.ownerId] }),
        getUsersPublic({ upkeepCompanyId: ctx.user.upkeepCompanyId, objectId: [sop.approverId] }),
        sop.locationId ? searchLocationsPublic({
          upkeepCompanyId: ctx.user.upkeepCompanyId,
          objectId: [sop.locationId],
          limit: 1,
        }) : { result: [] },
        sop.assetIds && sop.assetIds.length > 0
          ? searchAssetsPublic({
              upkeepCompanyId: ctx.user.upkeepCompanyId,
              objectId: sop.assetIds,
              limit: 100,
            })
          : { result: [] },
      ]);

      return {
        ...sop,
        owner: owner?.result?.[0],
        approver: approver?.result?.[0],
        location: location?.result?.[0],
        assets: assets?.result || [],
      };
    }),
  getByInstanceIdForEdit: privateProcedure
    .hasPermission(MODULES.SOP, ALLOWED_ACTIONS.VIEW)
    .input(GetSopByInstanceIdSchema)
    .query(async ({ input, ctx }) => {
      const sop = await getSopByInstanceId(input, ctx.user, ctx.needPartialCheck);

      if (!sop) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'SOP not found' });
      }

      return sop;
    }),
  getVersions: privateProcedure
    .hasPermission(MODULES.SOP, ALLOWED_ACTIONS.VIEW)
    .input(UuidSchema)
    .query(async ({ input, ctx }) => {
      const versions = await getSopVersions(input.id, ctx.user);

      const allUsers = Array.from(new Set(versions.map((version) => version.createdBy)));

      const users = await getUsersPublic({
        upkeepCompanyId: ctx.user.upkeepCompanyId,
        objectId: allUsers,
      });

      const userMap = users.result.reduce(
        (acc, user) => {
          acc[user.id] = user;
          return acc;
        },
        {} as Record<string, UserPublic>,
      );

      return versions.map((version) => ({
        ...version,
        createdBy: userMap[version.createdBy],
      }));
    }),
  updateStatus: privateProcedure
    .hasPermission(MODULES.SOP, ALLOWED_ACTIONS.EDIT)
    .input(UpdateSopStatusSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await updateSopStatus(input, ctx.user);
      if (!result) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to update SOP status' });
      }
      return result;
    }),
  minimalList: privateProcedure
    .hasPermission(MODULES.SOP, ALLOWED_ACTIONS.VIEW)
    .input(
      ListSopSchema.default({
        cursor: 0,
        limit: 10,
      }),
    )
    .query(async ({ input, ctx }) => {
      const paginatedSops = await listSops(input, ctx.user, ctx.needPartialCheck);

      // Only fetch locations if includeLocation is true and there are location IDs to search for
      const locationIds = Array.from(
        new Set(paginatedSops.result.map((sop) => sop.locationId).filter(Boolean)),
      ) as string[];

      const locations =
        input.includeLocation && locationIds.length > 0
          ? await searchLocationsPublic({
              upkeepCompanyId: ctx.user.upkeepCompanyId,
              objectId: locationIds,
              limit: 100,
            })
          : { result: [] };

      return {
        ...paginatedSops,
        result: paginatedSops.result.map((sop) => ({
          ...sop,
          location: input.includeLocation
            ? locations.result.find((location) => location.id === sop.locationId)
            : undefined,
        })),
      };
    }),
  list: privateProcedure
    .hasPermission(MODULES.SOP, ALLOWED_ACTIONS.VIEW)
    .input(
      ListSopSchema.default({
        cursor: 0,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      }),
    )
    .query(async ({ ctx, input }) => {
      const paginatedSops = await listSops(input, ctx.user, ctx.needPartialCheck);

      const { ownerIds, locationIds, assetIds } = paginatedSops.result.reduce(
        (acc, sop) => {
          if (sop.ownerId) acc.ownerIds.add(sop.ownerId);
          if (sop.locationId) acc.locationIds.add(sop.locationId);
          sop.assetIds?.forEach((id) => acc.assetIds.add(id));
          return acc;
        },
        {
          ownerIds: new Set<string>(),
          locationIds: new Set<string>(),
          assetIds: new Set<string>(),
        },
      );

      const ownerIdsArray = Array.from(ownerIds);
      const locationIdsArray = Array.from(locationIds);
      const assetIdsArray = Array.from(assetIds);

      const [ownersResponse, locationsResponse, assetsResponse] = await Promise.all([
        ownerIdsArray.length > 0
          ? getUsersPublic({
              upkeepCompanyId: ctx.user.upkeepCompanyId,
              objectId: ownerIdsArray,
              limit: 100,
            })
          : { result: [] },
        locationIdsArray.length > 0
          ? searchLocationsPublic({
              upkeepCompanyId: ctx.user.upkeepCompanyId,
              objectId: locationIdsArray,
              limit: 100,
            })
          : { result: [] },
        assetIdsArray.length > 0
          ? searchAssetsPublic({
              upkeepCompanyId: ctx.user.upkeepCompanyId,
              objectId: assetIdsArray,
              limit: 100,
            })
          : { result: [] },
      ]);

      const ownersMap = (ownersResponse?.result || []).reduce(
        (acc, owner) => {
          acc[owner.id] = owner;
          return acc;
        },
        {} as Record<string, (typeof ownersResponse.result)[0]>,
      );

      const locationsMap = (locationsResponse?.result || []).reduce(
        (acc, location) => {
          acc[location.id] = location;
          return acc;
        },
        {} as Record<string, Location>,
      );

      const assetsMap = (assetsResponse?.result || []).reduce(
        (acc, asset) => {
          acc[asset.id] = asset;
          return acc;
        },
        {} as Record<string, Asset>,
      );

      const sopsWithRelatedData = paginatedSops.result.map((sop) => {
        const owner = ownersMap[sop.ownerId];
        const location = sop.locationId ? locationsMap[sop.locationId] : undefined;
        const assets = sop.assetIds?.map((id) => assetsMap[id]).filter(Boolean) || [];

        return {
          ...sop,
          owner,
          location,
          assets,
        };
      });

      return {
        ...paginatedSops,
        result: sopsWithRelatedData,
      };
    }),
  toggleArchive: privateProcedure
    .hasPermission(MODULES.SOP, ALLOWED_ACTIONS.EDIT)
    .input(UuidSchema)
    .mutation(async ({ input, ctx }) => {
      const result = await toggleArchiveSop(input, ctx.user);
      if (!result) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Failed to archive/unarchive SOP' });
      }
      return result;
    }),
});
