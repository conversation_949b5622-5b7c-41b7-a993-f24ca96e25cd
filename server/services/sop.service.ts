import { db } from '@server/db';
import { processSopResult } from '@server/services/sop-processing.service';
import { createPaginatedResponse } from '@server/utils/pagination';
import { auditTrail, sops, sopSection, approvalStatusEnum, auditTrailActionEnum } from '@shared/schema';
import { UuidSchema } from '@shared/types/schema.types';
import {
  CreateSopFormSchema,
  CreateSopType,
  UpdateSopFormSchema,
  UpdateSopType,
  ListSopSchema,
  GetSopByInstanceIdSchema,
  UpdateSopStatus,
} from '@shared/types/sop.types';
import { User } from '@shared/types/users.types';
import { z } from 'zod';
import { and, eq, asc, desc, gte, ilike, inArray, isNull, lt, or, sql } from 'drizzle-orm';

export const createSop = async (input: z.infer<typeof CreateSopFormSchema>, user: User) => {
  return db.transaction(async (tx) => {
    // Process the SOP result to bulk create hazards and control measures within the transaction
    const processedSopResult = await processSopResult<CreateSopType>(input, user, tx);

    const { sop: sopInput, sections } = processedSopResult;

    // Calculate highestSeverity based on the sections (max severity × likelihood from all sections)
    const highestSeverity = sections.reduce((max, section) => {
      const riskScore = (section.severity ?? 0) * (section.likelihood ?? 1);
      return Math.max(max, riskScore);
    }, 0);

    // Create the SOP
    const [sopData] = await tx
      .insert(sops)
      .values({
        ...sopInput,
        upkeepCompanyId: user.upkeepCompanyId,
        createdBy: user.id,
        highestSeverity,
      })
      .returning({
        id: sops.id,
        instanceId: sops.instanceId,
        slug: sops.slug,
        version: sops.version,
        status: sops.status,
      });

    if (!sopData?.id) {
      return;
    }

    // Create the sections
    const sectionsData = sections.map((section) => ({
      ...section,
      sopId: sopData.id,
      upkeepCompanyId: user.upkeepCompanyId,
      createdBy: user.id,
    }));
    await tx.insert(sopSection).values(sectionsData);

    const action = sopData.status === approvalStatusEnum.enumValues[1] ? 'in_review' : 'drafted';

    // Create audit trail with the data created
    await tx.insert(auditTrail).values({
      entityType: 'sop',
      entityId: sopData.id,
      details: JSON.stringify(processedSopResult),
      action,
      userId: user.id,
      upkeepCompanyId: user.upkeepCompanyId,
    });

    return sopData;
  });
};

export const updateSop = async (input: z.infer<typeof UpdateSopFormSchema>, user: User) => {
  return db.transaction(async (tx) => {
    // Process the SOP result to bulk create hazards and control measures within the transaction
    const processedSopResult = await processSopResult<UpdateSopType>(input, user, tx);

    const { sop: updatedSop, sections } = processedSopResult;

    // Calculate highestSeverity based on the sections (max severity × likelihood from all sections)
    const highestSeverity = sections.reduce((max, section) => {
      const riskScore = (section.severity ?? 0) * (section.likelihood ?? 1);
      return Math.max(max, riskScore);
    }, 0);

    const updated = await tx
      .update(sops)
      .set({
        ...updatedSop,
        highestSeverity,
      })
      .where(and(eq(sops.id, input.sop.id), eq(sops.upkeepCompanyId, user.upkeepCompanyId)))
      .returning({
        id: sops.id,
        status: sops.status,
      });

    const sopRecord = updated.at(0);

    if (!sopRecord) {
      return;
    }

    // Update or insert sections based on whether they have an ID
    const updateSectionPromises = sections.map(async (section) => {
      if (section?.id) {
        // Update existing section
        return tx
          .update(sopSection)
          .set({
            ...section,
            sopId: sopRecord.id,
          })
          .where(eq(sopSection.id, section.id));
      } else {
        // Insert new section
        return tx.insert(sopSection).values({
          ...section,
          sopId: sopRecord.id,
          upkeepCompanyId: user.upkeepCompanyId,
          createdBy: user.id,
        });
      }
    });

    // Execute all section updates/inserts in parallel
    await Promise.all(updateSectionPromises);

    // Create audit trail entry
    await tx.insert(auditTrail).values({
      entityType: 'sop',
      entityId: sopRecord.id,
      action: 'updated',
      userId: user.id,
      upkeepCompanyId: user.upkeepCompanyId,
      details: JSON.stringify(sopRecord),
    });

    return sopRecord;
  });
};

export const listSops = async (input: z.infer<typeof ListSopSchema>, user: User, needPartialCheck = false) => {
  const {
    cursor = 0,
    limit = 10,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    search,
    status = [],
    ownerId = [],
    riskLevel,
    reviewStatus,
    includeArchived,
    locationIds = [],
    mustIncludeObjectIds,
  } = input;

  let query = db
    .select({
      id: sops.id,
      instanceId: sops.instanceId,
      slug: sops.slug,
      title: sops.title,
      ownerId: sops.ownerId,
      status: sops.status,
      reviewDate: sops.reviewDate,
      locationId: sops.locationId,
      assetIds: sops.assetIds,
      highestSeverity: sops.highestSeverity,
      version: sops.version,
      createdAt: sops.createdAt,
      archivedAt: sops.archivedAt,
      sectionCount: sql<number>`(
        SELECT COUNT(*)::int 
        FROM sop_section 
        WHERE sop_section.sop_id = sops.id 
        AND sop_section.upkeep_company_id = ${user.upkeepCompanyId}
      )`.as('sectionCount'),
      lastRevised: sops.updatedAt,
    })
    .from(sops)
    .where(
      and(
        eq(sops.upkeepCompanyId, user.upkeepCompanyId),
        needPartialCheck
          ? or(
              eq(sops.ownerId, user.id),
              eq(sops.approverId, user.id),
              eq(sops.createdBy, user.id),
              eq(sops.isPublic, true),
            )
          : undefined,
        !includeArchived ? isNull(sops.archivedAt) : undefined,
        search ? or(ilike(sops.title, `%${search}%`), ilike(sops.slug, `%${search}%`)) : undefined,
        status.length > 0 ? inArray(sops.status, status) : undefined,
        ownerId.length > 0 ? inArray(sops.ownerId, ownerId) : undefined,
        locationIds.length > 0 ? inArray(sops.locationId, locationIds) : undefined,
        // Risk level filter based on highestSeverity (SOP uses 1-4 scale)
        riskLevel === 'high' ? gte(sops.highestSeverity, 15) : undefined,
        riskLevel === 'medium' ? and(gte(sops.highestSeverity, 6), lt(sops.highestSeverity, 15)) : undefined,
        riskLevel === 'low' ? and(gte(sops.highestSeverity, 1), lt(sops.highestSeverity, 6)) : undefined,
        // Review status filter
        reviewStatus === 'overdue' ? lt(sops.reviewDate, new Date()) : undefined,
        reviewStatus === 'due_soon'
          ? and(gte(sops.reviewDate, new Date()), lt(sops.reviewDate, new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)))
          : undefined,
        reviewStatus === 'no_review_date' ? isNull(sops.reviewDate) : undefined,
      ),
    )
    .$dynamic();

  // Handle sorting
  if (mustIncludeObjectIds && mustIncludeObjectIds.length > 0) {
    query = query.orderBy(
      sql`CASE WHEN ${sops.id} IN (${mustIncludeObjectIds}) THEN 0 ELSE 1 END`,
      sortOrder === 'desc' ? desc(sops.createdAt) : asc(sops.createdAt),
    );
  } else {
    switch (sortBy) {
      case 'title':
        query = query.orderBy(sortOrder === 'desc' ? desc(sops.title) : asc(sops.title));
        break;
      case 'highestSeverity':
        query = query.orderBy(sortOrder === 'desc' ? desc(sops.highestSeverity) : asc(sops.highestSeverity));
        break;
      case 'reviewDate':
        query = query.orderBy(
          sortOrder === 'desc' ? desc(sops.reviewDate) : asc(sops.reviewDate),
          desc(sops.createdAt), // Secondary sort
        );
        break;
      case 'createdAt':
      default:
        query = query.orderBy(sortOrder === 'desc' ? desc(sops.createdAt) : asc(sops.createdAt));
        break;
    }
  }

  const filtered = await query.limit(limit).offset(cursor);

  return createPaginatedResponse(filtered, { cursor, limit });
};

export const toggleArchiveSop = async (input: z.infer<typeof UuidSchema>, user: User) => {
  return await db.transaction(async (tx) => {
    const updated = await tx
      .update(sops)
      .set({
        archivedAt: sql`CASE WHEN ${sops.archivedAt} IS NULL THEN NOW() ELSE NULL END`,
        updatedAt: sql`NOW()`,
      })
      .where(and(eq(sops.instanceId, input.id), eq(sops.upkeepCompanyId, user.upkeepCompanyId)))
      .returning({
        id: sops.id,
        instanceId: sops.instanceId,
        title: sops.title,
        archivedAt: sops.archivedAt,
        updatedAt: sops.updatedAt,
      });

    const sopRecord = updated.at(0);

    if (!sopRecord) {
      return null;
    }

    const action = sopRecord.archivedAt ? 'archived' : 'unarchived';
    await tx.insert(auditTrail).values({
      entityType: 'sop',
      entityId: sopRecord.id,
      action,
      userId: user.id,
      upkeepCompanyId: user.upkeepCompanyId,
      details: JSON.stringify(sopRecord),
    });

    return sopRecord;
  });
};

export const getSopByInstanceId = async (
  input: z.infer<typeof GetSopByInstanceIdSchema>,
  user: User,
  _needPartialCheck = false,
) => {
  const result = await db
    .select({
      // SOP fields
      id: sops.id,
      upkeepCompanyId: sops.upkeepCompanyId,
      slug: sops.slug,
      version: sops.version,
      instanceId: sops.instanceId,
      title: sops.title,
      purpose: sops.purpose,
      responsibilities: sops.responsibilities,
      ownerId: sops.ownerId,
      approverId: sops.approverId,
      status: sops.status,
      reviewDate: sops.reviewDate,
      locationId: sops.locationId,
      assetIds: sops.assetIds,
      highestSeverity: sops.highestSeverity,
      notes: sops.notes,
      isPublic: sops.isPublic,
      reasonForRevision: sops.reasonForRevision,
      createdBy: sops.createdBy,
      createdAt: sops.createdAt,
      updatedAt: sops.updatedAt,
      archivedAt: sops.archivedAt,
      // Aggregated sections with hazards and control measures
      sections: sql<
        Array<{
          id: string;
          upkeepCompanyId: string;
          sopId: string;
          sectionType: string;
          serial: number;
          label: string;
          value: string;
          hazardIds: string[] | null;
          controlMeasureIds: string[] | null;
          severity: number | null;
          likelihood: number | null;
          createdBy: string;
          createdAt: Date;
          updatedAt: Date;
          archivedAt: Date | null;
          hazards: Array<{
            id: string;
            upkeepCompanyId: string;
            name: string;
            type: string;
            createdBy: string;
            createdAt: Date;
            updatedAt: Date | null;
            archivedAt: Date | null;
          }>;
          controlMeasures: Array<{
            id: string;
            upkeepCompanyId: string;
            name: string;
            type: string;
            createdBy: string;
            createdAt: Date;
            updatedAt: Date | null;
            archivedAt: Date | null;
          }>;
        }>
      >`
        COALESCE(
          JSON_AGG(
            CASE 
              WHEN ${sopSection.id} IS NOT NULL 
              THEN JSON_BUILD_OBJECT(
                'id', ${sopSection.id},
                'upkeepCompanyId', ${sopSection.upkeepCompanyId},
                'sopId', ${sopSection.sopId},
                'sectionType', ${sopSection.sectionType},
                'serial', ${sopSection.serial},
                'label', ${sopSection.label},
                'value', ${sopSection.value},
                'hazardIds', ${sopSection.hazardIds},
                'controlMeasureIds', ${sopSection.controlMeasureIds},
                'severity', ${sopSection.severity},
                'likelihood', ${sopSection.likelihood},
                'createdBy', ${sopSection.createdBy},
                'createdAt', ${sopSection.createdAt},
                'updatedAt', ${sopSection.updatedAt},
                'archivedAt', ${sopSection.archivedAt},
                'hazards', COALESCE(
                  (
                    SELECT JSON_AGG(
                      JSON_BUILD_OBJECT(
                        'id', h.id,
                        'upkeepCompanyId', h.upkeep_company_id,
                        'name', h.name,
                        'type', h.type,
                        'createdBy', h.created_by,
                        'createdAt', h.created_at,
                        'updatedAt', h.updated_at,
                        'archivedAt', h.archived_at
                      )
                    )
                    FROM hazards h
                    WHERE h.id = ANY(${sopSection.hazardIds})
                      AND h.upkeep_company_id = ${user.upkeepCompanyId}
                      AND h.archived_at IS NULL
                  ),
                  '[]'::json
                ),
                'controlMeasures', COALESCE(
                  (
                    SELECT JSON_AGG(
                      JSON_BUILD_OBJECT(
                        'id', cm.id,
                        'upkeepCompanyId', cm.upkeep_company_id,
                        'name', cm.name,
                        'type', cm.type,
                        'createdBy', cm.created_by,
                        'createdAt', cm.created_at,
                        'updatedAt', cm.updated_at,
                        'archivedAt', cm.archived_at
                      )
                    )
                    FROM control_measures cm
                    WHERE cm.id = ANY(${sopSection.controlMeasureIds})
                      AND cm.upkeep_company_id = ${user.upkeepCompanyId}
                      AND cm.archived_at IS NULL
                  ),
                  '[]'::json
                )
              )
              ELSE NULL
            END
            ORDER BY ${sopSection.serial}
          ) FILTER (WHERE ${sopSection.id} IS NOT NULL),
          '[]'::json
        )
      `.as('sections'),
    })
    .from(sops)
    .leftJoin(sopSection, and(eq(sopSection.sopId, sops.id)))
    .where(
      and(
        eq(sops.instanceId, input.id),
        eq(sops.upkeepCompanyId, user.upkeepCompanyId),
        input.versionId ? eq(sops.id, input.versionId) : undefined,
      ),
    )
    .groupBy(
      sops.id,
      sops.upkeepCompanyId,
      sops.slug,
      sops.version,
      sops.instanceId,
      sops.title,
      sops.purpose,
      sops.responsibilities,
      sops.ownerId,
      sops.approverId,
      sops.status,
      sops.reviewDate,
      sops.locationId,
      sops.assetIds,
      sops.highestSeverity,
      sops.notes,
      sops.isPublic,
      sops.reasonForRevision,
      sops.createdBy,
      sops.createdAt,
      sops.updatedAt,
      sops.archivedAt,
    )
    .orderBy(desc(sops.createdAt))
    .limit(1);

  return result[0] || null;
};

export const getSopVersions = async (instanceId: string, user: User) => {
  return db
    .select({
      id: sops.id,
      instanceId: sops.instanceId,
      slug: sops.slug,
      version: sops.version,
      title: sops.title,
      status: sops.status,
      reviewDate: sops.reviewDate,
      reasonForRevision: sops.reasonForRevision,
      createdBy: sops.createdBy,
      updatedAt: sops.updatedAt,
    })
    .from(sops)
    .where(and(eq(sops.instanceId, instanceId), eq(sops.upkeepCompanyId, user.upkeepCompanyId)))
    .orderBy(desc(sops.createdAt));
};

export const updateSopStatus = async (input: UpdateSopStatus, user: User) => {
  return await db.transaction(async (tx) => {
    const currentDate = new Date().toISOString();
    const updated = await tx
      .update(sops)
      .set({
        status: input.status,
        updatedAt: new Date(),
        notes: input.rejectionReason
          ? sql`COALESCE(${sops.notes}, '') || CASE WHEN COALESCE(${sops.notes}, '') != '' THEN '\n\n' ELSE '' END || '--- REVIEWER COMMENTS --- ' || ${currentDate} || '\n' || ${input.rejectionReason}`
          : undefined,
      })
      .where(and(eq(sops.id, input.id), eq(sops.upkeepCompanyId, user.upkeepCompanyId)))
      .returning({
        id: sops.id,
        instanceId: sops.instanceId,
        slug: sops.slug,
        version: sops.version,
        title: sops.title,
        status: sops.status,
        locationId: sops.locationId,
        assetIds: sops.assetIds,
        createdAt: sops.createdAt,
        updatedAt: sops.updatedAt,
        createdBy: sops.createdBy,
        ownerId: sops.ownerId,
        approverId: sops.approverId,
        upkeepCompanyId: sops.upkeepCompanyId,
        reviewDate: sops.reviewDate,
        notes: sops.notes,
      });

    const sopRecord = updated.at(0);

    if (!sopRecord) {
      return null;
    }

    const statusMap: Record<string, (typeof auditTrailActionEnum.enumValues)[number]> = {
      [approvalStatusEnum.enumValues[2]]: 'approved',
      [approvalStatusEnum.enumValues[0]]: 'rejected',
      [approvalStatusEnum.enumValues[1]]: 'in_review',
    };

    const action = statusMap[sopRecord.status] || 'updated';

    await tx.insert(auditTrail).values({
      entityType: 'sop',
      entityId: sopRecord.id,
      action,
      userId: user.id,
      upkeepCompanyId: user.upkeepCompanyId,
      details: JSON.stringify(sopRecord),
    });

    return sopRecord;
  });
};
