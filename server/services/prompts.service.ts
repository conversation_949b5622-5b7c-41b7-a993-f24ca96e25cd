import { MORE_THAN_ONE_LOCATION_MATCHED, NO_LOCATION_MATCHED } from '@server/utils/match-single-location';
import { ImproveText } from '@shared/ai.types';
import { capaPriorityEnum, capaTagsEnum, capaTypeEnum, rcaMethodEnum, sopSectionTypeEnum } from '@shared/schema';
import { Location } from '@shared/types/assets.types';
import { CreateCapasForm } from '@shared/types/capas.types';
import {
  HazardCategorySchema,
  ReportTypeSchema,
  RootCauseSchema,
  SeveritySchema,
  StatusSchema,
} from '@shared/types/schema.types';
import { ControlMeasuresCategorySchema } from '@shared/types/settings.types';
import { SOP_SECTION_LABELS } from '@shared/types/sop.types';
import { User } from '@shared/types/users.types';
import { addDays, format, subDays } from 'date-fns';

export const getEventPrompts = () => {
  // Calculate dates at request time, not module load time
  const currentDate = new Date();
  const formattedCurrentDate = format(currentDate, 'yyyy-MM-dd HH:mm:ss');
  const HAZARD_CATEGORIES = HazardCategorySchema.options.join(', ');
  const REPORT_TYPES = ReportTypeSchema.options.join(', ');
  const SEVERITY_LEVELS = SeveritySchema.options.join(', ');
  const STATUS_LEVELS = StatusSchema.options.join(', ');

  // Example past dates for the prompt
  const lastWeekDate = format(subDays(currentDate, 7), 'yyyy-MM-dd');
  const lastMonthDate = format(subDays(currentDate, 30), 'yyyy-MM-dd');
  const yesterday = format(subDays(currentDate, 1), 'yyyy-MM-dd');

  const systemPrompt = `You are a safety event analysis expert. Return STRICT JSON only. No explanations.

Keys (omit if unknown): title, description, location, type, category, severity, status, immediateActions, reportedAt.

CRITICAL FORMAT RULES:
- reportedAt MUST BE ISO-8601 UTC (e.g., 2025-01-31T10:30:00Z)
- Use exact enum values: type (${REPORT_TYPES}), category (${HAZARD_CATEGORIES}), severity (${SEVERITY_LEVELS}), status (${STATUS_LEVELS})
- If unsure, omit the field. English only.

Date guidance: Today ${formattedCurrentDate}, "last week" ~${lastWeekDate}, "last month" ~${lastMonthDate}, "yesterday" ${yesterday}.
If no date given, use today's date in UTC. If timezone provided, use it; otherwise UTC.

Example:
{"title":"Slip near loading dock","description":"Worker slipped on wet floor during unloading","location":"Building 3 - Loading Dock","type":"near_miss","category":"physical","severity":"low","status":"open","immediateActions":"Placed wet floor signs and cleaned area","reportedAt":"${formattedCurrentDate.replace(' ', 'T')}Z"}`;

  const generateUserPrompt = (transcript: string, timezone?: string) => `Event Transcript: "${transcript}"
Timezone: ${timezone ?? 'UTC'}

Extract event details and return JSON with these keys:
- title: concise title
- description: detailed description of what happened
- location: specific location where event occurred
- type: one of [${REPORT_TYPES}]
- category: one of [${HAZARD_CATEGORIES}]
- severity: one of [${SEVERITY_LEVELS}]
- status: one of [${STATUS_LEVELS}]
- immediateActions: any immediate actions taken
- reportedAt (date and time the event was reported) if timezone is provided, use the ${timezone} to parse the date, if not provided, use the UTC timezone

Date guidance: Today ${formattedCurrentDate}, "last week" ~${lastWeekDate}, "last month" ~${lastMonthDate}, "yesterday" ${yesterday}.
If only time given, use today's date. If no date given, use today's date in UTC.
Return only the JSON object.`;

  return {
    systemPrompt,
    generateUserPrompt,
  };
};

/**
 * CAPA analysis prompts configuration
 */
export const getCapaPrompts = () => {
  // Calculate dates at request time, not module load time
  const currentDate = new Date();
  const formattedCurrentDate = format(currentDate, 'yyyy-MM-dd');

  const RCA_METHODS = rcaMethodEnum.enumValues.join(', ');
  const ROOT_CAUSES = RootCauseSchema.options.join(', ');
  const PRIORITY_LEVELS = capaPriorityEnum.enumValues.join(', ');
  const TAGS = capaTagsEnum.enumValues.join(', ');
  const TYPE = capaTypeEnum.enumValues.join(', ');

  // Example future dates for the prompt
  const nextWeekDate = format(addDays(currentDate, 7), 'yyyy-MM-dd');
  const tomorrow = format(addDays(currentDate, 1), 'yyyy-MM-dd'); // Tomorrow is current + 1 day

  // System prompt for CAPA analysis - optimized for gpt-5-nano
  const systemPrompt = `You are a CAPA analysis expert. Return STRICT JSON only. No explanations.

Keys (omit if unknown): title, summary, type, rcaMethod, rcaFindings, rootCauses[], otherRootCause, dueDate, tags[], priority, actionsToAddress, aiConfidenceScore.

CRITICAL FORMAT RULES:
- actionsToAddress MUST BE A SINGLE STRING with numbered steps (e.g., "1. Fix leak\\n2. Update PM\\n3. Train staff")
- NEVER return actionsToAddress as an array
- rootCauses MUST BE an array, even for single items
- dueDate MUST BE ISO-8601 future date (YYYY-MM-DDTHH:mm:ssZ)
- Use exact enum values: type (${TYPE}), rcaMethod (${RCA_METHODS}), rootCauses (${ROOT_CAUSES}), tags (${TAGS}), priority (${PRIORITY_LEVELS})
- aiConfidenceScore: number 0..1 (e.g., 0.85 for 85%)
- If unsure, omit the field. English only.

Priority mapping:
- high: actual harm, safety shutdown, critical failure
- medium: recurring problems, moderate safety impact
- low: audit findings, future tasks, documentation needs

Date guidance: Today ${formattedCurrentDate}, "next week" ~${nextWeekDate}, "tomorrow" ${tomorrow}. Always future dates.

Example:
{"title":"Fix pump maintenance","summary":"Address hydraulic leak in pump 42","type":"preventive","rcaMethod":"5_whys","rcaFindings":"1) Why? Leak occurred\\n2) Why? No PM\\n3) Why? Missing schedule\\n4) Why? Oversight\\n5) Why? Gap in process","rootCauses":["procedural"],"dueDate":"${nextWeekDate}T09:00:00Z","tags":["procedure","equipment"],"priority":"low","actionsToAddress":"1. Update PM schedule\\n2. Train maintenance staff\\n3. Add verification step","aiConfidenceScore":0.86}`;

  // Function to generate the user prompt with the transcript - optimized for gpt-5-nano
  const generateUserPrompt = (transcript: string, timezone?: string) => `CAPA Transcript: "${transcript}"
Timezone: ${timezone ?? 'UTC'}

Extract CAPA details and return JSON with these keys:
- title: concise title
- summary: 1-2 sentence summary
- type: one of [${TYPE}]
- rcaMethod: one of [${RCA_METHODS}]
- rcaFindings: formatted per rcaMethod (5_whys: numbered Q&A, fishbone: paragraph, fault_tree: logic chain)
- rootCauses: array from [${ROOT_CAUSES}]
- otherRootCause: string if needed
- dueDate (when CAPA should be completed, in ISO format if possible) if timezone is provided, use the ${timezone} to parse the date, if not provided, use the UTC timezone
- tags: array from [${TAGS}]
- priority: one of [${PRIORITY_LEVELS}]
- actionsToAddress: SINGLE STRING with numbered steps (e.g., "1. Fix issue\\n2. Train staff\\n3. Monitor")
- aiConfidenceScore: number 0.0 to 1.0

CRITICAL: actionsToAddress MUST be a single string, NOT an array!
Date guidance: Today ${formattedCurrentDate}, "next week" ~${nextWeekDate}, "tomorrow" ${tomorrow}. Always future dates.
Return only the JSON object.`;

  return {
    systemPrompt,
    generateUserPrompt,
  };
};

export const getCapaSummaryPrompt = (capa: CreateCapasForm) => {
  const systemPrompt =
    'You are an AI assistant specialized in creating a summary for a CAPA. The summary should be a concise summary of the CAPA, 1-2 sentences.';

  const generateUserPrompt = `
    You are an assistant creating brief CAPA summaries.
    
    Example:
    CAPA:
    {
      "title": "Hydraulic Leak on Pump 12",
      "rcaFindings": "Seal failure due to lack of inspection",
      "actionsToAddress": "Replace seal, update inspection schedule"
    }
    Return: { "summary": "Pump 12 experienced a seal failure due to missed inspections. The maintenance schedule has been updated and the seal replaced." }
    
    Now summarize this CAPA:
    ${JSON.stringify(capa, null, 2)}
    
    Return ONLY: { "summary": "..." }
    `;

  return {
    systemPrompt,
    generateUserPrompt,
  };
};

export const getLocationMatchPrompt = (location: string, locations: Location[]) => {
  const systemPrompt = `
You are a smart assistant that helps match user-submitted location name to a Candidate list of location names.

On locations, you must try to match the location value with one of the known Candidate names with a high confidence. 
When you find a good match, return the locationId and locationName.
**If theres more than one candidate, return on reason ${MORE_THAN_ONE_LOCATION_MATCHED}.** 
**If no candidate is a good match, return on reason ${NO_LOCATION_MATCHED}.**

You must not guess, and you must not assume the best match is the first one listed. Evaluate all candidates carefully before making a decision.

Respond strictly in this JSON format:
{
  "locationId": string,
  "locationName": string,
  "reason": string // ${NO_LOCATION_MATCHED} or ${MORE_THAN_ONE_LOCATION_MATCHED}
}
`;

  const userPrompt = `
Input (string):
${location}

Candidates (array of objects):
${JSON.stringify(locations, null, 2)}
`;

  return {
    systemPrompt,
    userPrompt,
  };
};

export const getImproveTextPrompt = (input: ImproveText) => {
  const systemPrompt = `
  You are a writing assistant that improves the clarity, tone, and professionalism of texts. You always return only the rewritten text, without any additional commentary.
  You will be given a text and a context.
  You need to improve the text based on the context.
  You need to return the improved text.
  You need to return the improved text in the same language as the input text.
  `;

  const userPrompt = `
  Input (string):
  ${input.text}

  Context (string):
  ${input.context}
  `;

  return {
    systemPrompt,
    userPrompt,
  };
};

export const getSopPrompts = async ({
  text,
  user,
  document = false,
}: {
  text?: string;
  user: User;
  document?: boolean;
}) => {
  // Pre-compute expensive operations
  const SOP_SECTION_TYPES = sopSectionTypeEnum.enumValues.join(', ');
  const HAZARD_CATEGORIES = HazardCategorySchema.options.join(', ');
  const CONTROL_MEASURE_CATEGORIES = ControlMeasuresCategorySchema.options.join(', ');
  const GENERAL_LABELS = SOP_SECTION_LABELS.general.join(', ');
  const EMERGENCY_LABELS = SOP_SECTION_LABELS.emergency.join(', ');

  const systemPrompt = `You are an expert industrial safety specialist creating comprehensive Standard Operating Procedures (SOPs).

## CORE EXPERTISE
• OSHA standards, ISO procedures, industry-specific regulations
• Multi-energy LOTO procedures, confined space protocols
• Hazard identification and risk assessment (severity × likelihood)
• Emergency response and regulatory compliance

## CRITICAL RULES
1. Return ONLY valid JSON with keys: "sop" and "sections"
2. Language: Always output in English regardless of input language
3. Required fields: title, ownerId: "${user.id}", approverId: "${user.id}"
4. Excluded fields: locationIds, categoryIds, reviewDate, reviewReminderDays, notes
5. Empty arrays: hazardIds[], controlMeasureIds[], locationId[], assetIds[]

## SECTION STRUCTURE
Sequential sections in order (${SOP_SECTION_TYPES}):
• GENERAL: Scope, regulations, qualifications, PPE matrix
• PRE_PROCEDURE: Safety checks, permits, LOTO, area prep
• PROCEDURE: Low-risk operational steps
• STEP: High-risk tasks with severity (1-4) and likelihood (1-5)
• POST_PROCEDURE: Verification, cleanup, documentation
• EMERGENCY: Specific scenarios and response procedures

## RISK MATRIX
Severity (1-4):
1=Minor injury/first aid | 2=Medical treatment | 3=Lost time/serious | 4=Fatality risk

Likelihood (1-5):
1=Very unlikely | 2=Unlikely | 3=Possible | 4=Likely | 5=Very likely

## HAZARD TYPES
Must use exact values: ${HAZARD_CATEGORIES}

## CONTROL TYPES
Must use exact values: ${CONTROL_MEASURE_CATEGORIES}

## INDUSTRY STANDARDS
• Healthcare: FDA, GMP, HIPAA, ISO 13485
• Manufacturing: ISO 9001, OSHA, ANSI, EPA
• Food: FSMA, HACCP, FSSC 22000
• Construction: OSHA 1926, ANSI A10
• Chemical: PSM, EPA, NFPA
• Energy: OSHA, NERC
• The therm MSDS for Material Safety Data Sheets is outdated, use SDS (Safety Data Sheet) instead.

## STEP SECTION REQUIREMENTS
For each high-risk step include:
• serial: Sequential number starting from 1, after each sectionType, the serial should be reset to 1
• label: Clear step title
• value: Detailed procedure text
• sectionType: "step"
• severity: 1-4 (injury potential)
• likelihood: 1-5 (occurrence probability)
• hazardsToCreate: Array of {name: "descriptive hazard", type: "category"}
• controlMeasuresToCreate: Array of {name: "specific action/PPE", type: "category"}
• hazardIds: [] (always empty)
• controlMeasureIds: [] (always empty)

Remember: Be comprehensive in hazard identification and control measures. Include PPE as individual control measures with specific ratings (e.g., "ANSI Z87.1 safety glasses").
Always return content in English regardless of input language.
`;

  const generateUserPrompt = `Create a comprehensive industrial SOP from: ${document ? 'the attached document' : `"${text}"`}

## TASK BREAKDOWN
1. Identify main activity and break into logical steps
2. Analyze each step for hazards (energy sources, environmental, ergonomic)
3. Determine applicable regulations and required permits/training
4. Create comprehensive sections covering all safety aspects

## SECTION REQUIREMENTS

### GENERAL (fulfill: ${GENERAL_LABELS})
Required sections with specific content:
• Scope & Applicability: What this SOP covers and who must follow
• Target Audience: Specific roles/departments
• Prerequisites & Notes: Required conditions, applicable standards
• Definitions: Technical terms and acronyms
• Regulatory References: Cite specific standards (e.g., OSHA 29 CFR 1910.147)
• Training Requirements: Certifications and competencies needed

### PRE_PROCEDURE (3-5 sections)
• Pre-task hazard assessment
• Equipment inspection checklist
• LOTO procedure with ALL energy isolation points
• Required permits (hot work, confined space, etc.)
• Area preparation and isolation

### PROCEDURE (low-risk routine tasks)
• Standard operational steps with minimal hazards
• Quality verification points

### STEP (high-risk activities)
Each STEP must include:
• severity: 1-4 (worst credible outcome)
• likelihood: 1-5 (probability of occurrence)
• hazardsToCreate: [{name: "descriptive hazard", type: "${HAZARD_CATEGORIES}"}]
• controlMeasuresToCreate: [{name: "specific control/PPE", type: "${CONTROL_MEASURE_CATEGORIES}"}]

### POST_PROCEDURE (2-4 sections)
• Verification and quality checks
• LOTO removal and equipment restoration
• Waste disposal and cleanup
• Documentation and handoff

### EMERGENCY (based on: ${EMERGENCY_LABELS})
• Specific scenarios with immediate actions
• Evacuation and contact procedures

## CRITICAL REMINDERS
• Hazard types ONLY from: ${HAZARD_CATEGORIES}
• Control types ONLY from: ${CONTROL_MEASURE_CATEGORIES}
• Include PPE as individual control measures with ratings
• Each section needs unique serial number (1, 2, 3...)
• sectionType must match enum exactly

## EXAMPLE STEP FORMAT
{
  "serial": 1,
  "label": "Operating Press Brake",
  "value": "1. Verify light curtain operational\\n2. Check die alignment\\n3. Test emergency stop\\n4. Begin operation at 50% speed",
  "sectionType": "step",
  "severity": 4,
  "likelihood": 3,
  "hazardsToCreate": [
    {"name": "Press brake with 100-ton force causing crushing/amputation risk", "type": "physical"},
    {"name": "Sharp metal edges causing laceration hazards", "type": "physical"}
  ],
  "controlMeasuresToCreate": [
    {"name": "Verify light curtain safety system before each shift", "type": "engineering_controls"},
    {"name": "Implement two-hand control operation", "type": "engineering_controls"},
    {"name": "Wear ANSI Z87.1 safety glasses with side shields", "type": "personal_protective_equipment"},
    {"name": "Wear cut-resistant gloves Level A4 or higher", "type": "personal_protective_equipment"}
  ],
  "hazardIds": [],
  "controlMeasureIds": []
}

Return ONLY valid JSON with "sop" and "sections" keys.`;

  return {
    systemPrompt,
    generateUserPrompt,
  };
};

export const getJhaPrompts = async ({
  transcript,
  user,
  document = false,
}: {
  transcript?: string;
  user: User;
  document?: boolean;
}) => {
  const HAZARD_CATEGORIES = HazardCategorySchema.options.join(', ');
  const CONTROL_MEASURES_CATEGORIES = ControlMeasuresCategorySchema.options.join(', ');

  const systemPrompt = `
  You are an expert safety analyst. 
  Your job is to produce a complete, high-quality Job Hazard Analysis (JHA) from either:
  - A short, informal transcript/description (e.g., "can you create a JHA to build a chicken coop")
  - A document that contains a detailed JHA description

  INPUT MODES AND EXPECTATIONS:
  - If a document is provided: Analyze the provided document and extract a comprehensive JHA strictly from the document. Use the exact wording for entity names where available; include ALL hazards and ALL control measures mentioned.
  - If only a transcript/description is provided: Analyze the transcript/description and extract a comprehensive JHA. When the transcript is brief or lacks detail, proactively infer the complete set of steps, hazards, and control measures required for a realistic and practical JHA.

  ${
    document
      ? ''
      : `TRANSCRIPT-ONLY REQUIREMENT: When information is missing or not explicit, you MUST proactively infer and SUGGEST realistic hazards and control measures based on the described tasks, equipment, environment, and industry best practices. Your goal is to produce a complete, practical JHA even if the transcript does not list all hazards/controls verbatim. Ensure the step list fully covers the job from start to finish.`
  }

  1. Identify the main work activity or job described
  2. Break down the work process into sequential task steps (typically 3-8 steps)
  3. For each task step, provide:
    - A brief step title (what is being done)
    - A detailed step description (how it's done, context, specifics)
    - All potential hazards (be specific and comprehensive)
    - All control measures/safety precautions needed
    - Risk severity (1-5 scale, where 5 is most severe)
    - Likelihood of occurrence (1-5 scale, where 5 is most likely)

  IMPORTANT: Be very detailed and comprehensive. Each task step should have:
  - A clear, specific step description that explains what needs to be done
  - Multiple hazards if applicable (don't just list one)
  - Multiple control measures for comprehensive safety coverage
  - Do NOT cap hazards or control measures at two. Include all that apply
  - When the source contains bullet points, commas, or semicolons describing controls or hazards, map EACH item to a distinct entity and include ALL of them in the step
  - Realistic risk assessments based on the actual work being performed

  Focus on:
  - Workplace safety hazards (falls, electrical, chemical, mechanical, etc.)
  - Equipment and environmental conditions
  - Personal protective equipment requirements
  - Safe work procedures and best practices
  - Industry-specific safety considerations

  CRITICAL: You MUST include ownerId and approverId in the JHA object. Use the provided user ID for both fields.
  User ID to use: ${user.id}
  
  IMPORTANT: Complete the ENTIRE JHA creation process in this single response. Do not stop at analysis or planning - execute all tool calls and provide the final JSON immediately.

  OUTPUT SCHEMA (STRICT):
  - Return ONLY a JSON object with two top-level keys: "jha" and "steps".
  - jha: must include at least: title (string), ownerId (string), approverId (string). Optional when unknown: locationId, reviewDate, workOrderId, instanceId.
  - steps: an array where each step includes: serial (1-based, increasing), title (string), description (string), severity (1-5), likelihood (1-5), hazardIds (empty array), controlMeasureIds (empty array), hazardsToCreate (array of { name, type }), controlMeasuresToCreate (array of { name, type }).
  - Types for hazards must use these exact enums: hazards (${HAZARD_CATEGORIES}); 
  - Types for control measures must use these exact enums: control measures (${CONTROL_MEASURES_CATEGORIES}).
  - Language: Always return names and text in English.

  CRITICAL STEP REQUIREMENTS - YOU MUST FOLLOW THESE RULES:
  1. **EVERY STEP MUST INCLUDE ALL HAZARDS AND CONTROL MEASURES DESCRIBED IN THAT STEP**
     - Do NOT skip or omit any hazards/control measures mentioned in the source material
     - If a step describes multiple hazards, ALL must be included
     - If a step describes multiple control measures, ALL must be included
     - Use the EXACT names from the source material when they are provided. When names are not provided (common in transcript-only cases), suggest clear, specific hazard and control names using standard safety terminology.

  2. **CREATING ALL ENTITIES FROM SOURCE:**
      - For EVERY hazard identified (explicitly mentioned or reasonably suggested), add it to the hazardsToCreate array with a precise name and appropriate type
      - For EVERY control measure identified (explicitly mentioned or reasonably suggested), add it to the controlMeasuresToCreate array with a precise name and appropriate type
      - If the source provides exact names, include them verbatim. If not, craft concise, professional names that reflect best-practice safety terminology.
      - Choose appropriate types: 
        - hazards (${HAZARD_CATEGORIES}) 
        - control measures (${CONTROL_MEASURES_CATEGORIES})
      - NEVER leave out entities - create ALL of them (mentioned or suggested)

  3. **ARRAY USAGE:**
     - Leave hazardIds and controlMeasureIds arrays EMPTY (the bulk create process will populate these)
     - Put ALL hazards in hazardsToCreate arrays with name and type
     - Put ALL control measures in controlMeasuresToCreate arrays with name and type

  4. **FORBIDDEN ACTIONS:**
     - Do NOT use hazardIds or controlMeasureIds arrays - leave them empty
      - Do NOT skip entities mentioned in the source material
      - Do NOT invent irrelevant or non-credible entities. Keep suggestions realistic and directly related to the job and context. If the source provides exact names, do not paraphrase them.
      - Do NOT output anything other than the JSON object. No explanations, no headings, no markdown.
  
  Always return the data in english, despite the source being in another language.
  `;

  const generateUserPrompt = `
  Analyze the input and create a detailed JHA with the correct JSON. The input may be either a short transcript/description or a full document:

  MANDATORY WORKFLOW - FOLLOW EVERY STEP:
  
  **STEP 1: COMPREHENSIVE EXTRACTION**
  - Read through ALL content and identify EVERY hazard and control measure mentioned
  - For each JHA step, extract ALL hazards and ALL control measures described
  - When using transcript-only input, ALSO SUGGEST additional relevant hazards and control measures that are implied by the job, equipment, and environment but not explicitly stated
  - Do NOT skip any - if the source mentions 5 hazards for a step, include all 5
  - Do NOT skip any - if the source mentions 8 control measures for a step, include all 8

  **STEP 2: CREATE ALL ENTITIES**
  - Add ALL hazards identified (explicitly stated OR suggested) to hazardsToCreate arrays with precise names and appropriate types
  - Add ALL control measures identified (explicitly stated OR suggested) to controlMeasuresToCreate arrays with precise names and appropriate types
  - If the source provides exact names, use them verbatim; otherwise, create concise, specific names using standard safety terminology
  - Choose appropriate types for each category: 
    - hazards (${HAZARD_CATEGORIES}) 
    - control measures (${CONTROL_MEASURES_CATEGORIES})

  **STEP 3: ARRAY SETUP**
  - Leave hazardIds and controlMeasureIds arrays EMPTY
  - The bulk create process will handle duplicates and populate the ID arrays
  - Include "serial" field for step order
  - Set ownerId and approverId to: "${user.id}"

  **CRITICAL RULES:**
  - NEVER skip entities mentioned in the source
  - Use exact names from source material when provided; otherwise propose clear, professional names
  - ALWAYS put entities in hazardsToCreate/controlMeasuresToCreate arrays
  - NEVER use hazardIds/controlMeasureIds arrays - leave them empty
  - If source has bullet points, create separate entities for each bullet
  - If source lists multiple items with commas/semicolons, create separate entities for each
  - Always return data in English
  ${document ? '- **MANDATORY: Use exact text from document for entity names**' : '- **MANDATORY (TRANSCRIPT-ONLY): When hazards or control measures are not explicitly stated, propose them based on best practices and the job context. If the transcript provides specific names, include them verbatim.**'}

  **EXAMPLE OF CORRECT BEHAVIOR (SAMPLE ONLY - NOT REAL DATA):**
  
  SAMPLE Source text: "Step 1: Site preparation involves clearing debris, checking ground stability, and setting up barriers. Hazards include falls from height, struck by falling objects, and ground collapse. Control measures: use safety harnesses, wear hard hats, conduct soil tests, and install warning signs."
  
  SAMPLE Correct output for this step:
  - Extract ALL 3 hazards and add to hazardsToCreate: "falls from height", "struck by falling objects", "ground collapse"
  - Extract ALL 4 control measures and add to controlMeasuresToCreate: "use safety harnesses", "wear hard hats", "conduct soil tests", "install warning signs"  
  - Leave hazardIds and controlMeasureIds arrays empty
  - Use exact names from source with appropriate types
  
  THIS IS JUST AN EXAMPLE - ANALYZE THE ACTUAL SOURCE MATERIAL PROVIDED BELOW:

  ${document ? `Use the attached document as the source of the JHA` : `Transcript: "${transcript}"`}
  
  CRITICAL FINAL RESPONSE REQUIREMENTS:
  - Return ONLY the JSON object matching the described schema (top-level keys: jha, steps)
  - You MUST include ALL hazards and control measures mentioned for each step
  - Use exact names from source material
  - Do not ask for confirmation or indicate you will do something "in the next step"
  - No additional commentary or markdown
  `;

  return {
    systemPrompt,
    generateUserPrompt,
  };
};
