import { db } from '@server/db';
import { createPaginatedResponse } from '@server/utils/pagination';
import { auditTrail, auditTrailActionEnum, events, files, statusEnum } from '@shared/schema';
import {
  CreateEventFormPublicSchema,
  CreateEventFormSchema,
  ExportEventsSchema,
  ListEventSchema,
  UpdateEventSchema,
} from '@shared/types/events.types';
import { TransientFileSchema } from '@shared/types/files.types';
import { IdSchema } from '@shared/types/schema.types';
import { User, UserPublic } from '@shared/types/users.types';
import { and, arrayContains, asc, desc, eq, getTableColumns, ilike, inArray, isNull, or, sql } from 'drizzle-orm';
import { z } from 'zod';

export const createEvent = async (input: z.infer<typeof CreateEventFormSchema>, user: User) => {
  return db.transaction(async (tx) => {
    const inserted = await tx
      .insert(events)
      .values({
        ...input,
        reportedBy: user.id,
        upkeepCompanyId: user.upkeepCompanyId,
        status: statusEnum.enumValues[0],
        reportedByName: user.fullName,
        reportedByEmail: user.email,
      })
      .returning({
        id: events.id,
        upkeepCompanyId: events.upkeepCompanyId,
        slug: events.slug,
        title: events.title,
        description: events.description,
        status: events.status,
        severity: events.severity,
        reportedAt: events.reportedAt,
        reportedBy: events.reportedBy,
        locationId: events.locationId,
        assetIds: events.assetIds,
        reportedByName: events.reportedByName,
        reportedByEmail: events.reportedByEmail,
        teamMembersToNotify: events.teamMembersToNotify,
        type: events.type,
        customerName: events.customerName,
        customerPhoneNumber: events.customerPhoneNumber,
        customerAddress: events.customerAddress,
      });

    const event = inserted.at(0);

    if (!event) {
      return;
    }

    await tx.insert(auditTrail).values({
      entityId: event.id,
      entityType: 'event',
      details: JSON.stringify(event),
      userId: event.reportedBy,
      upkeepCompanyId: inserted[0].upkeepCompanyId,
      timestamp: new Date(),
      action: auditTrailActionEnum.enumValues[0],
    });

    return event;
  });
};

export const createEventPublic = async (
  input: z.infer<typeof CreateEventFormPublicSchema>,
  user?: Partial<UserPublic>,
) => {
  return await db.transaction(async (tx) => {
    const inserted = await tx
      .insert(events)
      .values({
        ...input,
        reportedBy: user?.id,
        upkeepCompanyId: input.upkeepCompanyId,
        status: 'open',
        reportedByName: user?.fullName,
        reportedByEmail: user?.email,
      })
      .returning({
        id: events.id,
        upkeepCompanyId: events.upkeepCompanyId,
        slug: events.slug,
        title: events.title,
        description: events.description,
        status: events.status,
        severity: events.severity,
        reportedAt: events.reportedAt,
        reportedBy: events.reportedBy,
        locationId: events.locationId,
        assetIds: events.assetIds,
        reportedByName: events.reportedByName,
        reportedByEmail: events.reportedByEmail,
        teamMembersToNotify: events.teamMembersToNotify,
        type: events.type,
        customerName: events.customerName,
        customerPhoneNumber: events.customerPhoneNumber,
        customerAddress: events.customerAddress,
      });

    const event = inserted.at(0);

    if (!event) {
      return;
    }

    await tx.insert(auditTrail).values({
      entityId: event.id,
      entityType: 'event',
      details: JSON.stringify({ event, user, public: true }),
      userId: event.reportedBy,
      upkeepCompanyId: event.upkeepCompanyId,
      timestamp: new Date(),
      action: auditTrailActionEnum.enumValues[0],
    });

    return event;
  });
};

export const getEventById = async (id: string, user: User, needPartialCheck: boolean) => {
  // Fetch the safety event that matches the ID and company ID, and is not deleted
  const result = await db
    .select({
      ...getTableColumns(events),
      media: sql<z.infer<typeof TransientFileSchema>[]>`
      CASE
        WHEN COUNT(${files.id}) = 0 THEN NULL
        ELSE json_agg(
          json_build_object(
            'id', ${files.id},
            'name', ${files.fileName},
            'url', ${files.presignedUrl},
            'type', ${files.mimeType},
            'size', ${files.fileSize}
          )
        )
      END
    `,
    })
    .from(events)
    .leftJoin(files, and(eq(files.entityId, events.id), eq(files.status, 'completed'), eq(files.entityType, 'event')))
    .where(
      and(
        eq(events.id, id),
        eq(events.upkeepCompanyId, user.upkeepCompanyId),
        isNull(events.deletedAt),
        ...(needPartialCheck
          ? [or(eq(events.reportedBy, user.id), arrayContains(events.teamMembersToNotify, [user.id]))]
          : []),
      ),
    )
    .groupBy(events.id);

  return result.at(0);
};

export const listEvents = async (
  input: z.infer<typeof ListEventSchema> & { cursor?: number },
  user: User,
  needPartialCheck: boolean,
) => {
  const {
    cursor = 0,
    limit = 10,
    includeArchived,
    search,
    sortBy = 'reportedAt',
    sortOrder = 'desc',
    oshaReportable,
    severity = [],
    status = [],
    type = [],
    locationIds = [],
    mustIncludeObjectIds,
  } = input;

  let query = db
    .select({
      id: events.id,
      title: events.title,
      slug: events.slug,
      status: events.status,
      severity: events.severity,
      reportedBy: events.reportedBy,
      reportedByName: events.reportedByName,
      reportedByEmail: events.reportedByEmail,
      type: events.type,
      oshaReportable: events.oshaReportable,
      upkeepCompanyId: events.upkeepCompanyId,
      locationId: events.locationId,
      assetIds: events.assetIds,
      teamMembersToNotify: events.teamMembersToNotify,
      archivedAt: events.archivedAt,
      reportedAt: events.reportedAt,
      updatedAt: events.updatedAt,
      deletedAt: events.deletedAt,
      customerName: events.customerName,
      customerPhoneNumber: events.customerPhoneNumber,
      customerAddress: events.customerAddress,
    })
    .from(events)
    .where(
      and(
        eq(events.upkeepCompanyId, user.upkeepCompanyId),
        needPartialCheck
          ? or(eq(events.reportedBy, user.id), arrayContains(events.teamMembersToNotify, [user.id]))
          : undefined,
        !includeArchived ? isNull(events.archivedAt) : undefined,
        search
          ? or(
              ilike(events.title, `%${search}%`),
              ilike(events.description, `%${search}%`),
              ilike(events.slug, `%${search}%`),
            )
          : undefined,
        oshaReportable !== undefined && oshaReportable !== null ? eq(events.oshaReportable, oshaReportable) : undefined,
        severity.length > 0 ? inArray(events.severity, severity) : undefined,
        status.length > 0 ? inArray(events.status, status) : undefined,
        type.length > 0 ? inArray(events.type, type) : undefined,
        locationIds.length > 0 ? inArray(events.locationId, locationIds) : undefined,
      ),
    )
    .$dynamic();

  if (mustIncludeObjectIds && mustIncludeObjectIds.length > 0) {
    query = query.orderBy(
      sql`CASE WHEN ${events.id} IN (${mustIncludeObjectIds}) THEN 0 ELSE 1 END`,
      sortOrder === 'desc' ? desc(events.reportedAt) : asc(events.reportedAt),
    );
  } else if (sortBy) {
    query = query.orderBy(sortOrder === 'desc' ? desc(events.reportedAt) : asc(events.reportedAt));
  }

  const filtered = await query.limit(limit).offset(cursor);

  return createPaginatedResponse(filtered, { cursor, limit });
};

export const exportEvents = async (input: z.infer<typeof ExportEventsSchema>, user: User) => {
  const { search, status = [], type = [], severity = [], locationIds = [], includeArchived, oshaReportable } = input;

  const data = await db
    .select({
      id: events.id,
      title: events.title,
      slug: events.slug,
      status: events.status,
      severity: events.severity,
      reportedBy: events.reportedBy,
      reportedByName: events.reportedByName,
      reportedByEmail: events.reportedByEmail,
      type: events.type,
      category: events.category,
      oshaReportable: events.oshaReportable,
      upkeepCompanyId: events.upkeepCompanyId,
      locationId: events.locationId,
      assetIds: events.assetIds,
      teamMembersToNotify: events.teamMembersToNotify,
      archivedAt: events.archivedAt,
      reportedAt: events.reportedAt,
      updatedAt: events.updatedAt,
      deletedAt: events.deletedAt,
      customerName: events.customerName,
      customerPhoneNumber: events.customerPhoneNumber,
      customerAddress: events.customerAddress,
    })
    .from(events)
    .where(
      and(
        eq(events.upkeepCompanyId, user.upkeepCompanyId),
        !includeArchived ? isNull(events.archivedAt) : undefined,
        search
          ? or(
              ilike(events.title, `%${search}%`),
              ilike(events.description, `%${search}%`),
              ilike(events.slug, `%${search}%`),
            )
          : undefined,
        oshaReportable !== undefined && oshaReportable !== null ? eq(events.oshaReportable, oshaReportable) : undefined,
        severity.length > 0 ? inArray(events.severity, severity) : undefined,
        status.length > 0 ? inArray(events.status, status) : undefined,
        type.length > 0 ? inArray(events.type, type) : undefined,
        locationIds.length > 0 ? inArray(events.locationId, locationIds) : undefined,
      ),
    )
    .orderBy(events.reportedAt)
    .limit(500);

  return data;
};

export const updateEvent = async (
  id: string,
  event: z.infer<typeof UpdateEventSchema>,
  user: User,
  needPartialCheck: boolean,
) => {
  return db.transaction(async (tx) => {
    const updated = await tx
      .update({ ...events, updatedAt: new Date() })
      .set(event)
      .where(
        and(
          eq(events.id, id),
          eq(events.upkeepCompanyId, user.upkeepCompanyId),
          ...(needPartialCheck ? [eq(events.reportedBy, user.id)] : []),
        ),
      )
      .returning({
        id: events.id,
        status: events.status,
        archivedAt: events.archivedAt,
        reportedBy: events.reportedBy,
        upkeepCompanyId: events.upkeepCompanyId,
        slug: events.slug,
        title: events.title,
        description: events.description,
        severity: events.severity,
        reportedAt: events.reportedAt,
        locationId: events.locationId,
        assetIds: events.assetIds,
        reportedByName: events.reportedByName,
        reportedByEmail: events.reportedByEmail,
        teamMembersToNotify: events.teamMembersToNotify,
        type: events.type,
        customerName: events.customerName,
        customerPhoneNumber: events.customerPhoneNumber,
        customerAddress: events.customerAddress,
      });

    const updatedEvent = updated.at(0);

    if (!updatedEvent) {
      return;
    }

    let action: (typeof auditTrailActionEnum.enumValues)[number] = 'updated';

    if ('status' in event && 'status' in updatedEvent) {
      const statusMap = {
        open: auditTrailActionEnum.enumValues[7],
        in_review: auditTrailActionEnum.enumValues[6],
        closed: auditTrailActionEnum.enumValues[5],
      };

      action = statusMap[
        updatedEvent.status as keyof typeof statusMap
      ] as (typeof auditTrailActionEnum.enumValues)[number];
    }

    if ('archivedAt' in event && 'archivedAt' in updatedEvent) {
      action = updatedEvent.archivedAt ? 'archived' : 'unarchived';
    }

    if ('severity' in event && 'severity' in updatedEvent) {
      action = updatedEvent.severity ? 'updated' : 'updated';
    }

    await tx.insert(auditTrail).values({
      entityId: updatedEvent.id,
      entityType: 'event',
      details: JSON.stringify(updatedEvent),
      userId: updatedEvent.reportedBy,
      upkeepCompanyId: updatedEvent.upkeepCompanyId,
      timestamp: new Date(),
      action,
    });

    return {
      ...updatedEvent,
      action,
    };
  });
};

export const toggleArchiveEvent = async (input: z.infer<typeof IdSchema>, user: User) => {
  return await db.transaction(async (tx) => {
    const updated = await tx
      .update(events)
      .set({
        updatedAt: new Date(),
        archivedAt: sql`CASE WHEN ${events.archivedAt} IS NOT NULL THEN NULL ELSE NOW() END`,
      })
      .where(eq(events.id, input.id))
      .returning({
        id: events.id,
        status: events.status,
        archivedAt: events.archivedAt,
        reportedBy: events.reportedBy,
        upkeepCompanyId: events.upkeepCompanyId,
        slug: events.slug,
        title: events.title,
        description: events.description,
        severity: events.severity,
        reportedAt: events.reportedAt,
        locationId: events.locationId,
        assetIds: events.assetIds,
        reportedByName: events.reportedByName,
        reportedByEmail: events.reportedByEmail,
        teamMembersToNotify: events.teamMembersToNotify,
        type: events.type,
      });

    const event = updated.at(0);

    if (!event) {
      return;
    }

    const action = event.archivedAt ? 'archived' : 'unarchived';
    await tx.insert(auditTrail).values({
      entityId: event.id,
      entityType: 'event',
      details: JSON.stringify(event),
      userId: user.id,
      upkeepCompanyId: user.upkeepCompanyId,
      timestamp: new Date(),
      action,
    });

    return event;
  });
};
