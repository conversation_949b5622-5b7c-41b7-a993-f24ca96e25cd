import { openai } from '@ai-sdk/openai';
import { logger } from '@server/utils/logger';
import { Location } from '@shared/types/assets.types';
import { CreateCapasForm, CreateCapasSchema } from '@shared/types/capas.types';
import { CreateJhaAiSchema } from '@shared/types/jha.types';
import { HazardCategorySchema, ReportTypeSchema, SeveritySchema, StatusSchema } from '@shared/types/schema.types';
import { User } from '@shared/types/users.types';
import { generateObject, experimental_transcribe as transcribe } from 'ai';
import { addDays } from 'date-fns';
import { OpenAIModel } from 'env';
import { z } from 'zod';
import {
  getCapaPrompts,
  getCapaSummaryPrompt,
  getEventPrompts,
  getImproveTextPrompt,
  getJhaPrompts,
  getLocationMatchPrompt,
  getSopPrompts,
} from './prompts.service';
import { ImproveText } from '@shared/ai.types';
import { CreateSopAiSchema } from '@shared/types/sop.types';

export const transcribeAudio = async (audio: Buffer) => {
  const transcript = await transcribe({
    model: openai.transcription('whisper-1'),
    audio: audio,
  });

  return transcript;
};

export const AnalyzeEventTranscriptSchema = z.object({
  title: z.string(),
  description: z.string(),
  category: HazardCategorySchema,
  severity: SeveritySchema.optional().prefault(SeveritySchema.enum.low),
  type: ReportTypeSchema.optional(),
  status: StatusSchema.optional(),
  immediateActions: z.string().optional(),
  reportedAt: z.iso.datetime().optional(),
});

export type AnalyzeEventTranscript = z.infer<typeof AnalyzeEventTranscriptSchema>;

export const analyzeEventTranscript = async (transcript: string, timezone?: string) => {
  const { systemPrompt, generateUserPrompt } = getEventPrompts();

  const result = await generateObject({
    model: openai(OpenAIModel.GPT_5_NANO),
    schema: AnalyzeEventTranscriptSchema,
    system: systemPrompt,
    prompt: generateUserPrompt(transcript, timezone),
    providerOptions: {
      openai: {
        reasoningEffort: 'low',
        textVerbosity: 'high',
      },
    },
  });

  return result.object;
};

// Start with the base CAPA creation schema, but remove fields that should NOT be provided by AI
const AnalyzeCapaTranscriptSchema = CreateCapasSchema.pick({
  rcaMethod: true,
  rcaFindings: true,
  rootCauses: true,
  otherRootCause: true,
  title: true,
  priority: true,
  actionsToAddress: true,
  tags: true,
  type: true,
  summary: true,
}).extend({
  // Use string datetime format for AI JSON Schema compatibility
  dueDate: z.iso.datetime().optional(),
});

export type AnalyzeCapaTranscript = z.infer<typeof AnalyzeCapaTranscriptSchema>;

export const analyzeCapaTranscript = async (transcript: string, timezone?: string): Promise<AnalyzeCapaTranscript> => {
  const { systemPrompt, generateUserPrompt } = getCapaPrompts();

  const result = await generateObject({
    model: openai(OpenAIModel.GPT_5_NANO),
    schema: AnalyzeCapaTranscriptSchema,
    system: systemPrompt,
    prompt: generateUserPrompt(transcript, timezone),
    providerOptions: {
      openai: {
        reasoningEffort: 'low',
        textVerbosity: 'high',
      },
    },
  });

  // Further validate that dueDate is in the future
  if (result.object.dueDate) {
    const parsedDate = new Date(result.object.dueDate);
    if (parsedDate < new Date()) {
      // If the parsed date is in the past, add appropriate days to make it future
      // This is a fallback in case the model still produces a past date
      const correctedDate = addDays(new Date(), 7); // Default to one week from now
      result.object.dueDate = correctedDate.toISOString();
    }
  }

  return result.object;
};

const CreateCapaSummarySchema = z.object({
  summary: z.string(),
});

export type CreateCapaSummary = z.infer<typeof CreateCapaSummarySchema>;

export const createCapaSummary = async (capa: CreateCapasForm): Promise<string> => {
  const { systemPrompt, generateUserPrompt } = getCapaSummaryPrompt(capa);

  try {
    const result = await generateObject({
      model: openai(OpenAIModel.GPT_5_NANO),
      schema: CreateCapaSummarySchema,
      system: systemPrompt,
      prompt: generateUserPrompt,
      providerOptions: {
        openai: {
          reasoningEffort: 'low',
          textVerbosity: 'high',
        },
      },
    });

    return result.object.summary;
  } catch (error) {
    logger.error('Error creating CAPA summary:', { error, capa });
    return '';
  }
};

const MatchSchema = z.object({
  locationId: z.string().optional(),
  locationName: z.string().optional(),
  reason: z.string().optional(),
});

export const matchLocationsWithAI = async (location: string, locations: Location[]) => {
  try {
    const { systemPrompt, userPrompt } = getLocationMatchPrompt(location, locations);

    const result = await generateObject({
      model: openai(OpenAIModel.GPT_5_NANO),
      schema: MatchSchema,
      system: systemPrompt,
      prompt: userPrompt,
      providerOptions: {
        openai: {
          reasoningEffort: 'low',
          textVerbosity: 'high',
        },
      },
    });

    return result.object;
  } catch (error) {
    logger.error('AI location matching failed:', { location, error });
    // Return empty result on failure
    return {
      locationId: undefined,
      locationName: undefined,
      reason: 'AI matching failed',
    };
  }
};

export const improveText = async (input: ImproveText) => {
  const { systemPrompt, userPrompt } = getImproveTextPrompt(input);

  const result = await generateObject({
    model: openai(OpenAIModel.GPT_4O),
    schema: z.object({
      improvedText: z.string(),
    }),
    system: systemPrompt,
    prompt: userPrompt,
  });

  return result.object.improvedText;
};

export const analyzeJhaTranscript = async (transcript: string, user: User) => {
  const { systemPrompt, generateUserPrompt } = await getJhaPrompts({ transcript, user });

  // using gpt-4.1 because gpt-5.nano is not working with the jha schema
  const model = openai(OpenAIModel.GPT_4_1);

  const result = await generateObject({
    model,
    schema: CreateJhaAiSchema,
    system: systemPrompt,
    prompt: generateUserPrompt,
  });

  return result.object;
};

export const analyzeJhaDocument = async (analyzeJhaDocumentInput: { document: Buffer; user: User }) => {
  const { document, user } = analyzeJhaDocumentInput;
  const { systemPrompt, generateUserPrompt } = await getJhaPrompts({ user, document: true });

  // using gpt-4.1 because gpt-5.nano is not working with the jha schema
  const model = openai(OpenAIModel.GPT_4_1);

  const result = await generateObject({
    model,
    schema: CreateJhaAiSchema,
    system: systemPrompt,
    messages: [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: generateUserPrompt,
          },
          {
            type: 'file',
            data: document,
            mediaType: 'application/pdf',
          },
        ],
      },
    ],
  });

  return result.object;
};

export const analyzeSopTranscript = async (analyzeSopTranscriptInput: { text: string; user: User }) => {
  const { text, user } = analyzeSopTranscriptInput;
  const { systemPrompt, generateUserPrompt } = await getSopPrompts({ text, user });

  // using gpt-4.1 because gpt-5.nano is not working with the sop schema
  const model = openai(OpenAIModel.GPT_4_1);

  const result = await generateObject({
    model,
    schema: CreateSopAiSchema,
    system: systemPrompt,
    prompt: generateUserPrompt,
  });

  return result.object;
};

export const analyzeSopDocument = async (analyzeSopDocumentInput: { document: Buffer; user: User }) => {
  const { document, user } = analyzeSopDocumentInput;
  const { systemPrompt, generateUserPrompt } = await getSopPrompts({ user, document: true });

  // using gpt-4.1 because gpt-5.nano is not working with the sop schema
  const model = openai(OpenAIModel.GPT_4_1);

  const result = await generateObject({
    model,
    schema: CreateSopAiSchema,
    system: systemPrompt,
    messages: [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: generateUserPrompt,
          },
          {
            type: 'file',
            data: document,
            mediaType: 'application/pdf',
          },
        ],
      },
    ],
  });

  return result.object;
};
